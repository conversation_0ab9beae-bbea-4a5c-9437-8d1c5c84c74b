package com.sgs.testdatabiz.facade.model.enums;

/**
 * @author: shawn.yang
 * @create: 2023-04-18 19:37
 */
public enum ReportParentTypeEnum {
    TRF("TRF","TRF"),
    ORDER("Order","ORDER");

    private final String code;
    private final String desc;

    ReportParentTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ReportParentTypeEnum from(String code){
        for (ReportParentTypeEnum value : values()) {
            if (value.code.equals(code)){
                return value;
            }
        }
        return null;
    }

}
