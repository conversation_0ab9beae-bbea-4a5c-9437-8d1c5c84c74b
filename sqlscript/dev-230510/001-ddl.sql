ALTER TABLE `tb_test_data_info`
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `rd_report_id`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_result_full_name` varchar(500) NULL COMMENT '测试项名称，拼接字段' AFTER `report_no`,
    ADD COLUMN `test_result_seq` int(11) NULL COMMENT '测试项顺序' AFTER `test_result_full_name`,
    ADD COLUMN `result_value` varchar(150) NULL COMMENT '测试结果' AFTER `test_result_seq`,
    ADD COLUMN `result_value_remark` varchar(500) NULL COMMENT '测试结果备注' AFTER `result_value`,
    ADD COLUMN `result_unit` varchar(1024) NULL COMMENT '测试结果单位' AFTER `result_value_remark`,
    ADD COLUMN `fail_flag` tinyint(1) NULL COMMENT '失败标识' AFTER `result_unit`,
    ADD COLUMN `limit_value_full_name` varchar(500) NULL COMMENT '参考标准，拼接字段' AFTER `fail_flag`;


CREATE TABLE `tb_test_data_info_ee_gz` (
                                           `Id` bigint(20) NOT NULL AUTO_INCREMENT,
                                           `ObjectRelId` varchar(36) DEFAULT NULL,
                                           `TestDataMatrixId` bigint(20) NOT NULL,
                                           `TestMatrixId` varchar(36) DEFAULT NULL,
                                           `AnalyteId` varchar(36) DEFAULT NULL,
                                           `AnalyteName` varchar(500) DEFAULT NULL COMMENT 'analyte别名',
                                           `Position` mediumtext COMMENT 'position Json',
                                           `AnalyteType` int(10) DEFAULT NULL COMMENT '0：General、1：Conclusion',
                                           `AnalyteCode` varchar(512) DEFAULT NULL,
                                           `AnalyteSeq` int(11) DEFAULT NULL,
                                           `ReportUnit` varchar(50) DEFAULT NULL,
                                           `TestValue` varchar(150) DEFAULT NULL,
                                           `CasNo` varchar(500) DEFAULT NULL,
                                           `ReportLimit` varchar(50) DEFAULT NULL,
                                           `LimitUnit` varchar(100) DEFAULT NULL COMMENT 'LimitUnit',
                                           `ConclusionId` varchar(50) DEFAULT NULL,
                                           `Languages` mediumtext COMMENT '多语言json',
                                           `ActiveIndicator` int(1) DEFAULT NULL COMMENT '0无效，1有效',
                                           `CreatedBy` varchar(50) DEFAULT NULL,
                                           `CreatedDate` datetime DEFAULT NULL,
                                           `ModifiedBy` varchar(50) DEFAULT NULL,
                                           `ModifiedDate` datetime DEFAULT NULL,
                                           `BizVersionId` char(32) NOT NULL COMMENT '表中字段MD5 hash 计算得出',
                                           `rd_report_id` bigint(20) DEFAULT NULL,
                                           `lab_id` bigint(20) DEFAULT NULL COMMENT 'Trims系统实验室标识',
                                           `order_no` varchar(50) DEFAULT NULL COMMENT '订单号',
                                           `report_no` varchar(50) DEFAULT NULL COMMENT '报告号',
                                           `test_result_full_name` varchar(500) DEFAULT NULL COMMENT '测试项名称，拼接字段',
                                           `test_result_seq` int(11) DEFAULT NULL COMMENT '测试项顺序',
                                           `result_value` varchar(150) DEFAULT NULL COMMENT '测试结果',
                                           `result_value_remark` varchar(500) DEFAULT NULL COMMENT '测试结果备注',
                                           `result_unit` varchar(1024) DEFAULT NULL COMMENT '测试结果单位',
                                           `fail_flag` tinyint(1) DEFAULT NULL COMMENT '失败标识',
                                           `limit_value_full_name` varchar(500) DEFAULT NULL COMMENT '参考标准，拼接字段',
                                           PRIMARY KEY (`Id`) USING BTREE,
                                           UNIQUE KEY `idx_BizVersionId` (`BizVersionId`) USING BTREE,
                                           KEY `idx_ObjectRelId` (`ObjectRelId`),
                                           KEY `idx_testdatamatrixid` (`TestDataMatrixId`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

ALTER TABLE `tb_test_data_info_hl_aj`
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `rd_report_id`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_result_full_name` varchar(500) NULL COMMENT '测试项名称，拼接字段' AFTER `report_no`,
    ADD COLUMN `test_result_seq` int(11) NULL COMMENT '测试项顺序' AFTER `test_result_full_name`,
    ADD COLUMN `result_value` varchar(150) NULL COMMENT '测试结果' AFTER `test_result_seq`,
    ADD COLUMN `result_value_remark` varchar(500) NULL COMMENT '测试结果备注' AFTER `result_value`,
    ADD COLUMN `result_unit` varchar(1024) NULL COMMENT '测试结果单位' AFTER `result_value_remark`,
    ADD COLUMN `fail_flag` tinyint(1) NULL COMMENT '失败标识' AFTER `result_unit`,
    ADD COLUMN `limit_value_full_name` varchar(500) NULL COMMENT '参考标准，拼接字段' AFTER `fail_flag`;


ALTER TABLE `tb_test_data_info_hl_cz`
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `rd_report_id`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_result_full_name` varchar(500) NULL COMMENT '测试项名称，拼接字段' AFTER `report_no`,
    ADD COLUMN `test_result_seq` int(11) NULL COMMENT '测试项顺序' AFTER `test_result_full_name`,
    ADD COLUMN `result_value` varchar(150) NULL COMMENT '测试结果' AFTER `test_result_seq`,
    ADD COLUMN `result_value_remark` varchar(500) NULL COMMENT '测试结果备注' AFTER `result_value`,
    ADD COLUMN `result_unit` varchar(1024) NULL COMMENT '测试结果单位' AFTER `result_value_remark`,
    ADD COLUMN `fail_flag` tinyint(1) NULL COMMENT '失败标识' AFTER `result_unit`,
    ADD COLUMN `limit_value_full_name` varchar(500) NULL COMMENT '参考标准，拼接字段' AFTER `fail_flag`;


ALTER TABLE `tb_test_data_info_hl_gz`
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `rd_report_id`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_result_full_name` varchar(500) NULL COMMENT '测试项名称，拼接字段' AFTER `report_no`,
    ADD COLUMN `test_result_seq` int(11) NULL COMMENT '测试项顺序' AFTER `test_result_full_name`,
    ADD COLUMN `result_value` varchar(150) NULL COMMENT '测试结果' AFTER `test_result_seq`,
    ADD COLUMN `result_value_remark` varchar(500) NULL COMMENT '测试结果备注' AFTER `result_value`,
    ADD COLUMN `result_unit` varchar(1024) NULL COMMENT '测试结果单位' AFTER `result_value_remark`,
    ADD COLUMN `fail_flag` tinyint(1) NULL COMMENT '失败标识' AFTER `result_unit`,
    ADD COLUMN `limit_value_full_name` varchar(500) NULL COMMENT '参考标准，拼接字段' AFTER `fail_flag`;


ALTER TABLE `tb_test_data_info_hl_hk`
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `rd_report_id`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_result_full_name` varchar(500) NULL COMMENT '测试项名称，拼接字段' AFTER `report_no`,
    ADD COLUMN `test_result_seq` int(11) NULL COMMENT '测试项顺序' AFTER `test_result_full_name`,
    ADD COLUMN `result_value` varchar(150) NULL COMMENT '测试结果' AFTER `test_result_seq`,
    ADD COLUMN `result_value_remark` varchar(500) NULL COMMENT '测试结果备注' AFTER `result_value`,
    ADD COLUMN `result_unit` varchar(1024) NULL COMMENT '测试结果单位' AFTER `result_value_remark`,
    ADD COLUMN `fail_flag` tinyint(1) NULL COMMENT '失败标识' AFTER `result_unit`,
    ADD COLUMN `limit_value_full_name` varchar(500) NULL COMMENT '参考标准，拼接字段' AFTER `fail_flag`;


ALTER TABLE `tb_test_data_info_hl_hz`
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `rd_report_id`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_result_full_name` varchar(500) NULL COMMENT '测试项名称，拼接字段' AFTER `report_no`,
    ADD COLUMN `test_result_seq` int(11) NULL COMMENT '测试项顺序' AFTER `test_result_full_name`,
    ADD COLUMN `result_value` varchar(150) NULL COMMENT '测试结果' AFTER `test_result_seq`,
    ADD COLUMN `result_value_remark` varchar(500) NULL COMMENT '测试结果备注' AFTER `result_value`,
    ADD COLUMN `result_unit` varchar(1024) NULL COMMENT '测试结果单位' AFTER `result_value_remark`,
    ADD COLUMN `fail_flag` tinyint(1) NULL COMMENT '失败标识' AFTER `result_unit`,
    ADD COLUMN `limit_value_full_name` varchar(500) NULL COMMENT '参考标准，拼接字段' AFTER `fail_flag`;


ALTER TABLE `tb_test_data_info_hl_nb`
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `rd_report_id`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_result_full_name` varchar(500) NULL COMMENT '测试项名称，拼接字段' AFTER `report_no`,
    ADD COLUMN `test_result_seq` int(11) NULL COMMENT '测试项顺序' AFTER `test_result_full_name`,
    ADD COLUMN `result_value` varchar(150) NULL COMMENT '测试结果' AFTER `test_result_seq`,
    ADD COLUMN `result_value_remark` varchar(500) NULL COMMENT '测试结果备注' AFTER `result_value`,
    ADD COLUMN `result_unit` varchar(1024) NULL COMMENT '测试结果单位' AFTER `result_value_remark`,
    ADD COLUMN `fail_flag` tinyint(1) NULL COMMENT '失败标识' AFTER `result_unit`,
    ADD COLUMN `limit_value_full_name` varchar(500) NULL COMMENT '参考标准，拼接字段' AFTER `fail_flag`;


ALTER TABLE `tb_test_data_info_hl_nj`
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `rd_report_id`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_result_full_name` varchar(500) NULL COMMENT '测试项名称，拼接字段' AFTER `report_no`,
    ADD COLUMN `test_result_seq` int(11) NULL COMMENT '测试项顺序' AFTER `test_result_full_name`,
    ADD COLUMN `result_value` varchar(150) NULL COMMENT '测试结果' AFTER `test_result_seq`,
    ADD COLUMN `result_value_remark` varchar(500) NULL COMMENT '测试结果备注' AFTER `result_value`,
    ADD COLUMN `result_unit` varchar(1024) NULL COMMENT '测试结果单位' AFTER `result_value_remark`,
    ADD COLUMN `fail_flag` tinyint(1) NULL COMMENT '失败标识' AFTER `result_unit`,
    ADD COLUMN `limit_value_full_name` varchar(500) NULL COMMENT '参考标准，拼接字段' AFTER `fail_flag`;


ALTER TABLE `tb_test_data_info_hl_qd`
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `rd_report_id`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_result_full_name` varchar(500) NULL COMMENT '测试项名称，拼接字段' AFTER `report_no`,
    ADD COLUMN `test_result_seq` int(11) NULL COMMENT '测试项顺序' AFTER `test_result_full_name`,
    ADD COLUMN `result_value` varchar(150) NULL COMMENT '测试结果' AFTER `test_result_seq`,
    ADD COLUMN `result_value_remark` varchar(500) NULL COMMENT '测试结果备注' AFTER `result_value`,
    ADD COLUMN `result_unit` varchar(1024) NULL COMMENT '测试结果单位' AFTER `result_value_remark`,
    ADD COLUMN `fail_flag` tinyint(1) NULL COMMENT '失败标识' AFTER `result_unit`,
    ADD COLUMN `limit_value_full_name` varchar(500) NULL COMMENT '参考标准，拼接字段' AFTER `fail_flag`;


ALTER TABLE `tb_test_data_info_hl_sd`
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `rd_report_id`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_result_full_name` varchar(500) NULL COMMENT '测试项名称，拼接字段' AFTER `report_no`,
    ADD COLUMN `test_result_seq` int(11) NULL COMMENT '测试项顺序' AFTER `test_result_full_name`,
    ADD COLUMN `result_value` varchar(150) NULL COMMENT '测试结果' AFTER `test_result_seq`,
    ADD COLUMN `result_value_remark` varchar(500) NULL COMMENT '测试结果备注' AFTER `result_value`,
    ADD COLUMN `result_unit` varchar(1024) NULL COMMENT '测试结果单位' AFTER `result_value_remark`,
    ADD COLUMN `fail_flag` tinyint(1) NULL COMMENT '失败标识' AFTER `result_unit`,
    ADD COLUMN `limit_value_full_name` varchar(500) NULL COMMENT '参考标准，拼接字段' AFTER `fail_flag`;



ALTER TABLE `tb_test_data_info_hl_sh`
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `rd_report_id`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_result_full_name` varchar(500) NULL COMMENT '测试项名称，拼接字段' AFTER `report_no`,
    ADD COLUMN `test_result_seq` int(11) NULL COMMENT '测试项顺序' AFTER `test_result_full_name`,
    ADD COLUMN `result_value` varchar(150) NULL COMMENT '测试结果' AFTER `test_result_seq`,
    ADD COLUMN `result_value_remark` varchar(500) NULL COMMENT '测试结果备注' AFTER `result_value`,
    ADD COLUMN `result_unit` varchar(1024) NULL COMMENT '测试结果单位' AFTER `result_value_remark`,
    ADD COLUMN `fail_flag` tinyint(1) NULL COMMENT '失败标识' AFTER `result_unit`,
    ADD COLUMN `limit_value_full_name` varchar(500) NULL COMMENT '参考标准，拼接字段' AFTER `fail_flag`;



ALTER TABLE `tb_test_data_info_hl_sz`
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `rd_report_id`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_result_full_name` varchar(500) NULL COMMENT '测试项名称，拼接字段' AFTER `report_no`,
    ADD COLUMN `test_result_seq` int(11) NULL COMMENT '测试项顺序' AFTER `test_result_full_name`,
    ADD COLUMN `result_value` varchar(150) NULL COMMENT '测试结果' AFTER `test_result_seq`,
    ADD COLUMN `result_value_remark` varchar(500) NULL COMMENT '测试结果备注' AFTER `result_value`,
    ADD COLUMN `result_unit` varchar(1024) NULL COMMENT '测试结果单位' AFTER `result_value_remark`,
    ADD COLUMN `fail_flag` tinyint(1) NULL COMMENT '失败标识' AFTER `result_unit`,
    ADD COLUMN `limit_value_full_name` varchar(500) NULL COMMENT '参考标准，拼接字段' AFTER `fail_flag`;


ALTER TABLE `tb_test_data_info_hl_tc`
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `rd_report_id`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_result_full_name` varchar(500) NULL COMMENT '测试项名称，拼接字段' AFTER `report_no`,
    ADD COLUMN `test_result_seq` int(11) NULL COMMENT '测试项顺序' AFTER `test_result_full_name`,
    ADD COLUMN `result_value` varchar(150) NULL COMMENT '测试结果' AFTER `test_result_seq`,
    ADD COLUMN `result_value_remark` varchar(500) NULL COMMENT '测试结果备注' AFTER `result_value`,
    ADD COLUMN `result_unit` varchar(1024) NULL COMMENT '测试结果单位' AFTER `result_value_remark`,
    ADD COLUMN `fail_flag` tinyint(1) NULL COMMENT '失败标识' AFTER `result_unit`,
    ADD COLUMN `limit_value_full_name` varchar(500) NULL COMMENT '参考标准，拼接字段' AFTER `fail_flag`;


ALTER TABLE `tb_test_data_info_hl_tj`
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `rd_report_id`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_result_full_name` varchar(500) NULL COMMENT '测试项名称，拼接字段' AFTER `report_no`,
    ADD COLUMN `test_result_seq` int(11) NULL COMMENT '测试项顺序' AFTER `test_result_full_name`,
    ADD COLUMN `result_value` varchar(150) NULL COMMENT '测试结果' AFTER `test_result_seq`,
    ADD COLUMN `result_value_remark` varchar(500) NULL COMMENT '测试结果备注' AFTER `result_value`,
    ADD COLUMN `result_unit` varchar(1024) NULL COMMENT '测试结果单位' AFTER `result_value_remark`,
    ADD COLUMN `fail_flag` tinyint(1) NULL COMMENT '失败标识' AFTER `result_unit`,
    ADD COLUMN `limit_value_full_name` varchar(500) NULL COMMENT '参考标准，拼接字段' AFTER `fail_flag`;



ALTER TABLE `tb_test_data_info_hl_tp`
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `rd_report_id`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_result_full_name` varchar(500) NULL COMMENT '测试项名称，拼接字段' AFTER `report_no`,
    ADD COLUMN `test_result_seq` int(11) NULL COMMENT '测试项顺序' AFTER `test_result_full_name`,
    ADD COLUMN `result_value` varchar(150) NULL COMMENT '测试结果' AFTER `test_result_seq`,
    ADD COLUMN `result_value_remark` varchar(500) NULL COMMENT '测试结果备注' AFTER `result_value`,
    ADD COLUMN `result_unit` varchar(1024) NULL COMMENT '测试结果单位' AFTER `result_value_remark`,
    ADD COLUMN `fail_flag` tinyint(1) NULL COMMENT '失败标识' AFTER `result_unit`,
    ADD COLUMN `limit_value_full_name` varchar(500) NULL COMMENT '参考标准，拼接字段' AFTER `fail_flag`;


ALTER TABLE `tb_test_data_info_hl_xm`
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `rd_report_id`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_result_full_name` varchar(500) NULL COMMENT '测试项名称，拼接字段' AFTER `report_no`,
    ADD COLUMN `test_result_seq` int(11) NULL COMMENT '测试项顺序' AFTER `test_result_full_name`,
    ADD COLUMN `result_value` varchar(150) NULL COMMENT '测试结果' AFTER `test_result_seq`,
    ADD COLUMN `result_value_remark` varchar(500) NULL COMMENT '测试结果备注' AFTER `result_value`,
    ADD COLUMN `result_unit` varchar(1024) NULL COMMENT '测试结果单位' AFTER `result_value_remark`,
    ADD COLUMN `fail_flag` tinyint(1) NULL COMMENT '失败标识' AFTER `result_unit`,
    ADD COLUMN `limit_value_full_name` varchar(500) NULL COMMENT '参考标准，拼接字段' AFTER `fail_flag`;


ALTER TABLE `tb_test_data_info_sl_cz`
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `rd_report_id`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_result_full_name` varchar(500) NULL COMMENT '测试项名称，拼接字段' AFTER `report_no`,
    ADD COLUMN `test_result_seq` int(11) NULL COMMENT '测试项顺序' AFTER `test_result_full_name`,
    ADD COLUMN `result_value` varchar(150) NULL COMMENT '测试结果' AFTER `test_result_seq`,
    ADD COLUMN `result_value_remark` varchar(500) NULL COMMENT '测试结果备注' AFTER `result_value`,
    ADD COLUMN `result_unit` varchar(1024) NULL COMMENT '测试结果单位' AFTER `result_value_remark`,
    ADD COLUMN `fail_flag` tinyint(1) NULL COMMENT '失败标识' AFTER `result_unit`,
    ADD COLUMN `limit_value_full_name` varchar(500) NULL COMMENT '参考标准，拼接字段' AFTER `fail_flag`;


ALTER TABLE `tb_test_data_info_sl_gz`
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `rd_report_id`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_result_full_name` varchar(500) NULL COMMENT '测试项名称，拼接字段' AFTER `report_no`,
    ADD COLUMN `test_result_seq` int(11) NULL COMMENT '测试项顺序' AFTER `test_result_full_name`,
    ADD COLUMN `result_value` varchar(150) NULL COMMENT '测试结果' AFTER `test_result_seq`,
    ADD COLUMN `result_value_remark` varchar(500) NULL COMMENT '测试结果备注' AFTER `result_value`,
    ADD COLUMN `result_unit` varchar(1024) NULL COMMENT '测试结果单位' AFTER `result_value_remark`,
    ADD COLUMN `fail_flag` tinyint(1) NULL COMMENT '失败标识' AFTER `result_unit`,
    ADD COLUMN `limit_value_full_name` varchar(500) NULL COMMENT '参考标准，拼接字段' AFTER `fail_flag`;



ALTER TABLE `tb_test_data_info_sl_hk`
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `rd_report_id`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_result_full_name` varchar(500) NULL COMMENT '测试项名称，拼接字段' AFTER `report_no`,
    ADD COLUMN `test_result_seq` int(11) NULL COMMENT '测试项顺序' AFTER `test_result_full_name`,
    ADD COLUMN `result_value` varchar(150) NULL COMMENT '测试结果' AFTER `test_result_seq`,
    ADD COLUMN `result_value_remark` varchar(500) NULL COMMENT '测试结果备注' AFTER `result_value`,
    ADD COLUMN `result_unit` varchar(1024) NULL COMMENT '测试结果单位' AFTER `result_value_remark`,
    ADD COLUMN `fail_flag` tinyint(1) NULL COMMENT '失败标识' AFTER `result_unit`,
    ADD COLUMN `limit_value_full_name` varchar(500) NULL COMMENT '参考标准，拼接字段' AFTER `fail_flag`;



ALTER TABLE `tb_test_data_info_sl_hz`
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `rd_report_id`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_result_full_name` varchar(500) NULL COMMENT '测试项名称，拼接字段' AFTER `report_no`,
    ADD COLUMN `test_result_seq` int(11) NULL COMMENT '测试项顺序' AFTER `test_result_full_name`,
    ADD COLUMN `result_value` varchar(150) NULL COMMENT '测试结果' AFTER `test_result_seq`,
    ADD COLUMN `result_value_remark` varchar(500) NULL COMMENT '测试结果备注' AFTER `result_value`,
    ADD COLUMN `result_unit` varchar(1024) NULL COMMENT '测试结果单位' AFTER `result_value_remark`,
    ADD COLUMN `fail_flag` tinyint(1) NULL COMMENT '失败标识' AFTER `result_unit`,
    ADD COLUMN `limit_value_full_name` varchar(500) NULL COMMENT '参考标准，拼接字段' AFTER `fail_flag`;



CREATE TABLE `tb_test_data_info_sl_isb` (
                                            `Id` bigint(20) NOT NULL AUTO_INCREMENT,
                                            `ObjectRelId` varchar(36) DEFAULT NULL,
                                            `TestDataMatrixId` bigint(20) NOT NULL,
                                            `TestMatrixId` varchar(36) DEFAULT NULL,
                                            `AnalyteId` varchar(36) DEFAULT NULL,
                                            `AnalyteName` varchar(500) DEFAULT NULL COMMENT 'analyte别名',
                                            `Position` mediumtext COMMENT 'position Json',
                                            `AnalyteType` int(10) DEFAULT NULL COMMENT '0：General、1：Conclusion',
                                            `AnalyteCode` varchar(512) DEFAULT NULL,
                                            `AnalyteSeq` int(11) DEFAULT NULL,
                                            `ReportUnit` varchar(50) DEFAULT NULL,
                                            `TestValue` varchar(50) DEFAULT NULL,
                                            `CasNo` varchar(100) DEFAULT NULL,
                                            `ReportLimit` varchar(50) DEFAULT NULL,
                                            `LimitUnit` varchar(100) DEFAULT NULL COMMENT 'LimitUnit',
                                            `ConclusionId` varchar(50) DEFAULT NULL,
                                            `Languages` mediumtext COMMENT '多语言json',
                                            `ActiveIndicator` int(1) DEFAULT NULL COMMENT '0无效，1有效',
                                            `CreatedBy` varchar(50) DEFAULT NULL,
                                            `CreatedDate` datetime DEFAULT NULL,
                                            `ModifiedBy` varchar(50) DEFAULT NULL,
                                            `ModifiedDate` datetime DEFAULT NULL,
                                            `BizVersionId` char(32) NOT NULL COMMENT '表中字段MD5 hash 计算得出',
                                            `rd_report_id` bigint(20) DEFAULT NULL,
                                            `lab_id` bigint(20) DEFAULT NULL COMMENT 'Trims系统实验室标识',
                                            `order_no` varchar(50) DEFAULT NULL COMMENT '订单号',
                                            `report_no` varchar(50) DEFAULT NULL COMMENT '报告号',
                                            `test_result_full_name` varchar(500) DEFAULT NULL COMMENT '测试项名称，拼接字段',
                                            `test_result_seq` int(11) DEFAULT NULL COMMENT '测试项顺序',
                                            `result_value` varchar(150) DEFAULT NULL COMMENT '测试结果',
                                            `result_value_remark` varchar(500) DEFAULT NULL COMMENT '测试结果备注',
                                            `result_unit` varchar(1024) DEFAULT NULL COMMENT '测试结果单位',
                                            `fail_flag` tinyint(1) DEFAULT NULL COMMENT '失败标识',
                                            `limit_value_full_name` varchar(500) DEFAULT NULL COMMENT '参考标准，拼接字段',
                                            PRIMARY KEY (`Id`) USING BTREE,
                                            UNIQUE KEY `idx_BizVersionId` (`BizVersionId`) USING BTREE,
                                            KEY `idx_ObjectRelId` (`ObjectRelId`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;


ALTER TABLE `tb_test_data_info_sl_nb`
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `rd_report_id`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_result_full_name` varchar(500) NULL COMMENT '测试项名称，拼接字段' AFTER `report_no`,
    ADD COLUMN `test_result_seq` int(11) NULL COMMENT '测试项顺序' AFTER `test_result_full_name`,
    ADD COLUMN `result_value` varchar(150) NULL COMMENT '测试结果' AFTER `test_result_seq`,
    ADD COLUMN `result_value_remark` varchar(500) NULL COMMENT '测试结果备注' AFTER `result_value`,
    ADD COLUMN `result_unit` varchar(1024) NULL COMMENT '测试结果单位' AFTER `result_value_remark`,
    ADD COLUMN `fail_flag` tinyint(1) NULL COMMENT '失败标识' AFTER `result_unit`,
    ADD COLUMN `limit_value_full_name` varchar(500) NULL COMMENT '参考标准，拼接字段' AFTER `fail_flag`;


ALTER TABLE `tb_test_data_info_sl_nj`
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `rd_report_id`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_result_full_name` varchar(500) NULL COMMENT '测试项名称，拼接字段' AFTER `report_no`,
    ADD COLUMN `test_result_seq` int(11) NULL COMMENT '测试项顺序' AFTER `test_result_full_name`,
    ADD COLUMN `result_value` varchar(150) NULL COMMENT '测试结果' AFTER `test_result_seq`,
    ADD COLUMN `result_value_remark` varchar(500) NULL COMMENT '测试结果备注' AFTER `result_value`,
    ADD COLUMN `result_unit` varchar(1024) NULL COMMENT '测试结果单位' AFTER `result_value_remark`,
    ADD COLUMN `fail_flag` tinyint(1) NULL COMMENT '失败标识' AFTER `result_unit`,
    ADD COLUMN `limit_value_full_name` varchar(500) NULL COMMENT '参考标准，拼接字段' AFTER `fail_flag`;


ALTER TABLE `tb_test_data_info_sl_qd`
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `rd_report_id`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_result_full_name` varchar(500) NULL COMMENT '测试项名称，拼接字段' AFTER `report_no`,
    ADD COLUMN `test_result_seq` int(11) NULL COMMENT '测试项顺序' AFTER `test_result_full_name`,
    ADD COLUMN `result_value` varchar(150) NULL COMMENT '测试结果' AFTER `test_result_seq`,
    ADD COLUMN `result_value_remark` varchar(500) NULL COMMENT '测试结果备注' AFTER `result_value`,
    ADD COLUMN `result_unit` varchar(1024) NULL COMMENT '测试结果单位' AFTER `result_value_remark`,
    ADD COLUMN `fail_flag` tinyint(1) NULL COMMENT '失败标识' AFTER `result_unit`,
    ADD COLUMN `limit_value_full_name` varchar(500) NULL COMMENT '参考标准，拼接字段' AFTER `fail_flag`;


ALTER TABLE `tb_test_data_info_sl_sh`
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `rd_report_id`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_result_full_name` varchar(500) NULL COMMENT '测试项名称，拼接字段' AFTER `report_no`,
    ADD COLUMN `test_result_seq` int(11) NULL COMMENT '测试项顺序' AFTER `test_result_full_name`,
    ADD COLUMN `result_value` varchar(150) NULL COMMENT '测试结果' AFTER `test_result_seq`,
    ADD COLUMN `result_value_remark` varchar(500) NULL COMMENT '测试结果备注' AFTER `result_value`,
    ADD COLUMN `result_unit` varchar(1024) NULL COMMENT '测试结果单位' AFTER `result_value_remark`,
    ADD COLUMN `fail_flag` tinyint(1) NULL COMMENT '失败标识' AFTER `result_unit`,
    ADD COLUMN `limit_value_full_name` varchar(500) NULL COMMENT '参考标准，拼接字段' AFTER `fail_flag`;



ALTER TABLE `tb_test_data_info_sl_tj`
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `rd_report_id`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_result_full_name` varchar(500) NULL COMMENT '测试项名称，拼接字段' AFTER `report_no`,
    ADD COLUMN `test_result_seq` int(11) NULL COMMENT '测试项顺序' AFTER `test_result_full_name`,
    ADD COLUMN `result_value` varchar(150) NULL COMMENT '测试结果' AFTER `test_result_seq`,
    ADD COLUMN `result_value_remark` varchar(500) NULL COMMENT '测试结果备注' AFTER `result_value`,
    ADD COLUMN `result_unit` varchar(1024) NULL COMMENT '测试结果单位' AFTER `result_value_remark`,
    ADD COLUMN `fail_flag` tinyint(1) NULL COMMENT '失败标识' AFTER `result_unit`,
    ADD COLUMN `limit_value_full_name` varchar(500) NULL COMMENT '参考标准，拼接字段' AFTER `fail_flag`;

ALTER TABLE `tb_test_data_info_sl_xm`
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `rd_report_id`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_result_full_name` varchar(500) NULL COMMENT '测试项名称，拼接字段' AFTER `report_no`,
    ADD COLUMN `test_result_seq` int(11) NULL COMMENT '测试项顺序' AFTER `test_result_full_name`,
    ADD COLUMN `result_value` varchar(150) NULL COMMENT '测试结果' AFTER `test_result_seq`,
    ADD COLUMN `result_value_remark` varchar(500) NULL COMMENT '测试结果备注' AFTER `result_value`,
    ADD COLUMN `result_unit` varchar(1024) NULL COMMENT '测试结果单位' AFTER `result_value_remark`,
    ADD COLUMN `fail_flag` tinyint(1) NULL COMMENT '失败标识' AFTER `result_unit`,
    ADD COLUMN `limit_value_full_name` varchar(500) NULL COMMENT '参考标准，拼接字段' AFTER `fail_flag`;



ALTER TABLE `tb_test_data_matrix_info`
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `ModifiedDate`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_matrix_group_id` int(11) NULL COMMENT '测试单位分组标识' AFTER `report_no`,
    ADD COLUMN `test_line_instance_id` varchar(64) NULL COMMENT '测试项实例标识' AFTER `test_matrix_group_id`,
    ADD COLUMN `evaluation_name` varchar(255) NULL COMMENT '测试项名称' AFTER `test_line_instance_id`,
    ADD COLUMN `test_line_status` varchar(255) NULL COMMENT '测试项状态' AFTER `evaluation_name`,
    ADD COLUMN `test_line_remark` varchar(255) NULL COMMENT '测试项备注' AFTER `test_line_status`,
    ADD COLUMN `citation_full_name` varchar(255) NULL COMMENT '测试标准拼接名称' AFTER `test_line_remark`,
    ADD COLUMN `sample_instance_id` varchar(36) NULL COMMENT '样品实例标识' AFTER `citation_full_name`,
    ADD COLUMN `sample_group` text NULL COMMENT '样品分组信息' AFTER `sample_instance_id`,
    ADD COLUMN `sample_parent_id` varchar(36) NULL COMMENT '样品父级标识' AFTER `sample_group`,
    ADD COLUMN `sample_type` varchar(255) NULL COMMENT '样品类型' AFTER `sample_parent_id`,
    ADD COLUMN `category` varchar(255) NULL COMMENT '样品分类' AFTER `sample_type`,
    ADD COLUMN `material_color` varchar(255) NULL COMMENT '物料颜色' AFTER `category`,
    ADD COLUMN `composition` varchar(255) NULL COMMENT '物料材质' AFTER `material_color`,
    ADD COLUMN `material_description` varchar(255) NULL COMMENT '物料描述' AFTER `composition`,
    ADD COLUMN `material_end_use` varchar(255) NULL COMMENT '物料用途' AFTER `material_description`,
    ADD COLUMN `applicable_flag` varchar(255) NULL COMMENT 'NC样品标识' AFTER `material_end_use`,
    ADD COLUMN `material_other_sample_info` varchar(255) NULL COMMENT '其他样品信息' AFTER `applicable_flag`,
    ADD COLUMN `material_remark` varchar(255) NULL COMMENT '样品备注信息' AFTER `material_other_sample_info`,
    ADD COLUMN `conclusion_code` varchar(255) NULL COMMENT '测试结论编码' AFTER `material_remark`,
    ADD COLUMN `customer_conclusion` varchar(50) NULL COMMENT '客户测试结论' AFTER `conclusion_code`,
    ADD COLUMN `conclusion_remark` varchar(255) NULL COMMENT '测试结论备注' AFTER `customer_conclusion`,
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `last_modified_timestamp` datetime(0) NULL ON
UPDATE CURRENT_TIMESTAMP(0) COMMENT ' DB 自动更新，不允许程序设置 ' AFTER `rd_report_id`;


CREATE TABLE `tb_test_data_matrix_info_ee_gz` (
                                                  `id` bigint(20) NOT NULL,
                                                  `ObjectRelId` varchar(36) DEFAULT NULL,
                                                  `TestMatrixId` varchar(36) DEFAULT NULL,
                                                  `TestLineMappingId` int(11) DEFAULT NULL,
                                                  `ExternalId` varchar(300) DEFAULT NULL,
                                                  `ExternalCode` varchar(50) DEFAULT NULL,
                                                  `PpVersionId` int(11) DEFAULT NULL,
                                                  `Aid` bigint(20) DEFAULT NULL,
                                                  `TestLineId` int(11) DEFAULT NULL,
                                                  `CitationId` int(11) DEFAULT NULL,
                                                  `CitationVersionId` int(11) DEFAULT NULL,
                                                  `CitationType` int(11) DEFAULT '0' COMMENT '0：None、1：Method、2：Regulation、3：Standard',
                                                  `CitationName` varchar(1000) DEFAULT NULL,
                                                  `SampleId` varchar(36) DEFAULT NULL,
                                                  `SampleNo` varchar(255) DEFAULT NULL COMMENT '样品编号',
                                                  `ExternalSampleNo` varchar(500) DEFAULT NULL,
                                                  `TestLineSeq` bigint(20) DEFAULT NULL,
                                                  `SampleSeq` varchar(36) DEFAULT NULL,
                                                  `ExtFields` mediumtext,
                                                  `Condition` mediumtext,
                                                  `EvaluationAlias` varchar(500) DEFAULT NULL,
                                                  `MethodDesc` varchar(512) DEFAULT NULL,
                                                  `ConclusionId` varchar(50) DEFAULT NULL,
                                                  `ConclusionDisplay` varchar(50) DEFAULT NULL,
                                                  `Languages` mediumtext,
                                                  `BizVersionId` char(32) NOT NULL,
                                                  `rd_report_id` bigint(20) DEFAULT NULL,
                                                  `last_modified_timestamp` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT ' DB 自动更新，不允许程序设置 ',
                                                  `ActiveIndicator` int(1) NOT NULL COMMENT '0无效，1有效',
                                                  `CreatedBy` varchar(50) DEFAULT NULL,
                                                  `CreatedDate` datetime DEFAULT NULL,
                                                  `ModifiedBy` varchar(50) CHARACTER SET utf8 DEFAULT NULL,
                                                  `ModifiedDate` datetime DEFAULT NULL,
                                                  `lab_id` bigint(20) DEFAULT NULL COMMENT 'Trims系统实验室标识',
                                                  `order_no` varchar(50) DEFAULT NULL COMMENT '订单号',
                                                  `report_no` varchar(50) DEFAULT NULL COMMENT '报告号',
                                                  `test_matrix_group_id` int(11) DEFAULT NULL COMMENT '测试单位分组标识',
                                                  `test_line_instance_id` varchar(64) DEFAULT NULL COMMENT '测试项实例标识',
                                                  `evaluation_name` varchar(255) DEFAULT NULL COMMENT '测试项名称',
                                                  `test_line_status` varchar(255) DEFAULT NULL COMMENT '测试项状态',
                                                  `test_line_remark` varchar(255) DEFAULT NULL COMMENT '测试项备注',
                                                  `citation_full_name` varchar(255) DEFAULT NULL COMMENT '测试标准拼接名称',
                                                  `sample_instance_id` varchar(36) DEFAULT NULL COMMENT '样品实例标识',
                                                  `sample_group` text COMMENT '样品分组信息',
                                                  `sample_parent_id` varchar(36) DEFAULT NULL COMMENT '样品父级标识',
                                                  `sample_type` varchar(255) DEFAULT NULL COMMENT '样品类型',
                                                  `category` varchar(255) DEFAULT NULL COMMENT '样品分类',
                                                  `material_color` varchar(255) DEFAULT NULL COMMENT '物料颜色',
                                                  `composition` varchar(255) DEFAULT NULL COMMENT '物料材质',
                                                  `material_description` varchar(255) DEFAULT NULL COMMENT '物料描述',
                                                  `material_end_use` varchar(255) DEFAULT NULL COMMENT '物料用途',
                                                  `applicable_flag` varchar(255) DEFAULT NULL COMMENT 'NC样品标识',
                                                  `material_other_sample_info` varchar(255) DEFAULT NULL COMMENT '其他样品信息',
                                                  `material_remark` varchar(255) DEFAULT NULL COMMENT '样品备注信息',
                                                  `conclusion_code` varchar(255) DEFAULT NULL COMMENT '测试结论编码',
                                                  `customer_conclusion` varchar(50) DEFAULT NULL COMMENT '客户测试结论',
                                                  `conclusion_remark` varchar(255) DEFAULT NULL COMMENT '测试结论备注',
                                                  PRIMARY KEY (`id`) USING BTREE,
                                                  UNIQUE KEY `idx_BizVersionId` (`BizVersionId`),
                                                  KEY `idx_ObjectRelId` (`ObjectRelId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


ALTER TABLE `tb_test_data_matrix_info_hl_aj`
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `ModifiedDate`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_matrix_group_id` int(11) NULL COMMENT '测试单位分组标识' AFTER `report_no`,
    ADD COLUMN `test_line_instance_id` varchar(64) NULL COMMENT '测试项实例标识' AFTER `test_matrix_group_id`,
    ADD COLUMN `evaluation_name` varchar(255) NULL COMMENT '测试项名称' AFTER `test_line_instance_id`,
    ADD COLUMN `test_line_status` varchar(255) NULL COMMENT '测试项状态' AFTER `evaluation_name`,
    ADD COLUMN `test_line_remark` varchar(255) NULL COMMENT '测试项备注' AFTER `test_line_status`,
    ADD COLUMN `citation_full_name` varchar(255) NULL COMMENT '测试标准拼接名称' AFTER `test_line_remark`,
    ADD COLUMN `sample_instance_id` varchar(36) NULL COMMENT '样品实例标识' AFTER `citation_full_name`,
    ADD COLUMN `sample_group` text NULL COMMENT '样品分组信息' AFTER `sample_instance_id`,
    ADD COLUMN `sample_parent_id` varchar(36) NULL COMMENT '样品父级标识' AFTER `sample_group`,
    ADD COLUMN `sample_type` varchar(255) NULL COMMENT '样品类型' AFTER `sample_parent_id`,
    ADD COLUMN `category` varchar(255) NULL COMMENT '样品分类' AFTER `sample_type`,
    ADD COLUMN `material_color` varchar(255) NULL COMMENT '物料颜色' AFTER `category`,
    ADD COLUMN `composition` varchar(255) NULL COMMENT '物料材质' AFTER `material_color`,
    ADD COLUMN `material_description` varchar(255) NULL COMMENT '物料描述' AFTER `composition`,
    ADD COLUMN `material_end_use` varchar(255) NULL COMMENT '物料用途' AFTER `material_description`,
    ADD COLUMN `applicable_flag` varchar(255) NULL COMMENT 'NC样品标识' AFTER `material_end_use`,
    ADD COLUMN `material_other_sample_info` varchar(255) NULL COMMENT '其他样品信息' AFTER `applicable_flag`,
    ADD COLUMN `material_remark` varchar(255) NULL COMMENT '样品备注信息' AFTER `material_other_sample_info`,
    ADD COLUMN `conclusion_code` varchar(255) NULL COMMENT '测试结论编码' AFTER `material_remark`,
    ADD COLUMN `customer_conclusion` varchar(50) NULL COMMENT '客户测试结论' AFTER `conclusion_code`,
    ADD COLUMN `conclusion_remark` varchar(255) NULL COMMENT '测试结论备注' AFTER `customer_conclusion`,
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `last_modified_timestamp` datetime(0) NULL ON
UPDATE CURRENT_TIMESTAMP(0) COMMENT ' DB 自动更新，不允许程序设置 ' AFTER `rd_report_id`;


ALTER TABLE `tb_test_data_matrix_info_hl_cz`
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `ModifiedDate`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_matrix_group_id` int(11) NULL COMMENT '测试单位分组标识' AFTER `report_no`,
    ADD COLUMN `test_line_instance_id` varchar(64) NULL COMMENT '测试项实例标识' AFTER `test_matrix_group_id`,
    ADD COLUMN `evaluation_name` varchar(255) NULL COMMENT '测试项名称' AFTER `test_line_instance_id`,
    ADD COLUMN `test_line_status` varchar(255) NULL COMMENT '测试项状态' AFTER `evaluation_name`,
    ADD COLUMN `test_line_remark` varchar(255) NULL COMMENT '测试项备注' AFTER `test_line_status`,
    ADD COLUMN `citation_full_name` varchar(255) NULL COMMENT '测试标准拼接名称' AFTER `test_line_remark`,
    ADD COLUMN `sample_instance_id` varchar(36) NULL COMMENT '样品实例标识' AFTER `citation_full_name`,
    ADD COLUMN `sample_group` text NULL COMMENT '样品分组信息' AFTER `sample_instance_id`,
    ADD COLUMN `sample_parent_id` varchar(36) NULL COMMENT '样品父级标识' AFTER `sample_group`,
    ADD COLUMN `sample_type` varchar(255) NULL COMMENT '样品类型' AFTER `sample_parent_id`,
    ADD COLUMN `category` varchar(255) NULL COMMENT '样品分类' AFTER `sample_type`,
    ADD COLUMN `material_color` varchar(255) NULL COMMENT '物料颜色' AFTER `category`,
    ADD COLUMN `composition` varchar(255) NULL COMMENT '物料材质' AFTER `material_color`,
    ADD COLUMN `material_description` varchar(255) NULL COMMENT '物料描述' AFTER `composition`,
    ADD COLUMN `material_end_use` varchar(255) NULL COMMENT '物料用途' AFTER `material_description`,
    ADD COLUMN `applicable_flag` varchar(255) NULL COMMENT 'NC样品标识' AFTER `material_end_use`,
    ADD COLUMN `material_other_sample_info` varchar(255) NULL COMMENT '其他样品信息' AFTER `applicable_flag`,
    ADD COLUMN `material_remark` varchar(255) NULL COMMENT '样品备注信息' AFTER `material_other_sample_info`,
    ADD COLUMN `conclusion_code` varchar(255) NULL COMMENT '测试结论编码' AFTER `material_remark`,
    ADD COLUMN `customer_conclusion` varchar(50) NULL COMMENT '客户测试结论' AFTER `conclusion_code`,
    ADD COLUMN `conclusion_remark` varchar(255) NULL COMMENT '测试结论备注' AFTER `customer_conclusion`,
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `last_modified_timestamp` datetime(0) NULL ON
UPDATE CURRENT_TIMESTAMP(0) COMMENT ' DB 自动更新，不允许程序设置 ' AFTER `rd_report_id`;

ALTER TABLE `tb_test_data_matrix_info_hl_gz`
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `ModifiedDate`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_matrix_group_id` int(11) NULL COMMENT '测试单位分组标识' AFTER `report_no`,
    ADD COLUMN `test_line_instance_id` varchar(64) NULL COMMENT '测试项实例标识' AFTER `test_matrix_group_id`,
    ADD COLUMN `evaluation_name` varchar(255) NULL COMMENT '测试项名称' AFTER `test_line_instance_id`,
    ADD COLUMN `test_line_status` varchar(255) NULL COMMENT '测试项状态' AFTER `evaluation_name`,
    ADD COLUMN `test_line_remark` varchar(255) NULL COMMENT '测试项备注' AFTER `test_line_status`,
    ADD COLUMN `citation_full_name` varchar(255) NULL COMMENT '测试标准拼接名称' AFTER `test_line_remark`,
    ADD COLUMN `sample_instance_id` varchar(36) NULL COMMENT '样品实例标识' AFTER `citation_full_name`,
    ADD COLUMN `sample_group` text NULL COMMENT '样品分组信息' AFTER `sample_instance_id`,
    ADD COLUMN `sample_parent_id` varchar(36) NULL COMMENT '样品父级标识' AFTER `sample_group`,
    ADD COLUMN `sample_type` varchar(255) NULL COMMENT '样品类型' AFTER `sample_parent_id`,
    ADD COLUMN `category` varchar(255) NULL COMMENT '样品分类' AFTER `sample_type`,
    ADD COLUMN `material_color` varchar(255) NULL COMMENT '物料颜色' AFTER `category`,
    ADD COLUMN `composition` varchar(255) NULL COMMENT '物料材质' AFTER `material_color`,
    ADD COLUMN `material_description` varchar(255) NULL COMMENT '物料描述' AFTER `composition`,
    ADD COLUMN `material_end_use` varchar(255) NULL COMMENT '物料用途' AFTER `material_description`,
    ADD COLUMN `applicable_flag` varchar(255) NULL COMMENT 'NC样品标识' AFTER `material_end_use`,
    ADD COLUMN `material_other_sample_info` varchar(255) NULL COMMENT '其他样品信息' AFTER `applicable_flag`,
    ADD COLUMN `material_remark` varchar(255) NULL COMMENT '样品备注信息' AFTER `material_other_sample_info`,
    ADD COLUMN `conclusion_code` varchar(255) NULL COMMENT '测试结论编码' AFTER `material_remark`,
    ADD COLUMN `customer_conclusion` varchar(50) NULL COMMENT '客户测试结论' AFTER `conclusion_code`,
    ADD COLUMN `conclusion_remark` varchar(255) NULL COMMENT '测试结论备注' AFTER `customer_conclusion`,
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `last_modified_timestamp` datetime(0) NULL ON
UPDATE CURRENT_TIMESTAMP(0) COMMENT ' DB 自动更新，不允许程序设置 ' AFTER `rd_report_id`;


ALTER TABLE `tb_test_data_matrix_info_hl_hk`
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `ModifiedDate`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_matrix_group_id` int(11) NULL COMMENT '测试单位分组标识' AFTER `report_no`,
    ADD COLUMN `test_line_instance_id` varchar(64) NULL COMMENT '测试项实例标识' AFTER `test_matrix_group_id`,
    ADD COLUMN `evaluation_name` varchar(255) NULL COMMENT '测试项名称' AFTER `test_line_instance_id`,
    ADD COLUMN `test_line_status` varchar(255) NULL COMMENT '测试项状态' AFTER `evaluation_name`,
    ADD COLUMN `test_line_remark` varchar(255) NULL COMMENT '测试项备注' AFTER `test_line_status`,
    ADD COLUMN `citation_full_name` varchar(255) NULL COMMENT '测试标准拼接名称' AFTER `test_line_remark`,
    ADD COLUMN `sample_instance_id` varchar(36) NULL COMMENT '样品实例标识' AFTER `citation_full_name`,
    ADD COLUMN `sample_group` text NULL COMMENT '样品分组信息' AFTER `sample_instance_id`,
    ADD COLUMN `sample_parent_id` varchar(36) NULL COMMENT '样品父级标识' AFTER `sample_group`,
    ADD COLUMN `sample_type` varchar(255) NULL COMMENT '样品类型' AFTER `sample_parent_id`,
    ADD COLUMN `category` varchar(255) NULL COMMENT '样品分类' AFTER `sample_type`,
    ADD COLUMN `material_color` varchar(255) NULL COMMENT '物料颜色' AFTER `category`,
    ADD COLUMN `composition` varchar(255) NULL COMMENT '物料材质' AFTER `material_color`,
    ADD COLUMN `material_description` varchar(255) NULL COMMENT '物料描述' AFTER `composition`,
    ADD COLUMN `material_end_use` varchar(255) NULL COMMENT '物料用途' AFTER `material_description`,
    ADD COLUMN `applicable_flag` varchar(255) NULL COMMENT 'NC样品标识' AFTER `material_end_use`,
    ADD COLUMN `material_other_sample_info` varchar(255) NULL COMMENT '其他样品信息' AFTER `applicable_flag`,
    ADD COLUMN `material_remark` varchar(255) NULL COMMENT '样品备注信息' AFTER `material_other_sample_info`,
    ADD COLUMN `conclusion_code` varchar(255) NULL COMMENT '测试结论编码' AFTER `material_remark`,
    ADD COLUMN `customer_conclusion` varchar(50) NULL COMMENT '客户测试结论' AFTER `conclusion_code`,
    ADD COLUMN `conclusion_remark` varchar(255) NULL COMMENT '测试结论备注' AFTER `customer_conclusion`,
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `last_modified_timestamp` datetime(0) NULL ON
UPDATE CURRENT_TIMESTAMP(0) COMMENT ' DB 自动更新，不允许程序设置 ' AFTER `rd_report_id`;


ALTER TABLE `tb_test_data_matrix_info_hl_hz`
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `ModifiedDate`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_matrix_group_id` int(11) NULL COMMENT '测试单位分组标识' AFTER `report_no`,
    ADD COLUMN `test_line_instance_id` varchar(64) NULL COMMENT '测试项实例标识' AFTER `test_matrix_group_id`,
    ADD COLUMN `evaluation_name` varchar(255) NULL COMMENT '测试项名称' AFTER `test_line_instance_id`,
    ADD COLUMN `test_line_status` varchar(255) NULL COMMENT '测试项状态' AFTER `evaluation_name`,
    ADD COLUMN `test_line_remark` varchar(255) NULL COMMENT '测试项备注' AFTER `test_line_status`,
    ADD COLUMN `citation_full_name` varchar(255) NULL COMMENT '测试标准拼接名称' AFTER `test_line_remark`,
    ADD COLUMN `sample_instance_id` varchar(36) NULL COMMENT '样品实例标识' AFTER `citation_full_name`,
    ADD COLUMN `sample_group` text NULL COMMENT '样品分组信息' AFTER `sample_instance_id`,
    ADD COLUMN `sample_parent_id` varchar(36) NULL COMMENT '样品父级标识' AFTER `sample_group`,
    ADD COLUMN `sample_type` varchar(255) NULL COMMENT '样品类型' AFTER `sample_parent_id`,
    ADD COLUMN `category` varchar(255) NULL COMMENT '样品分类' AFTER `sample_type`,
    ADD COLUMN `material_color` varchar(255) NULL COMMENT '物料颜色' AFTER `category`,
    ADD COLUMN `composition` varchar(255) NULL COMMENT '物料材质' AFTER `material_color`,
    ADD COLUMN `material_description` varchar(255) NULL COMMENT '物料描述' AFTER `composition`,
    ADD COLUMN `material_end_use` varchar(255) NULL COMMENT '物料用途' AFTER `material_description`,
    ADD COLUMN `applicable_flag` varchar(255) NULL COMMENT 'NC样品标识' AFTER `material_end_use`,
    ADD COLUMN `material_other_sample_info` varchar(255) NULL COMMENT '其他样品信息' AFTER `applicable_flag`,
    ADD COLUMN `material_remark` varchar(255) NULL COMMENT '样品备注信息' AFTER `material_other_sample_info`,
    ADD COLUMN `conclusion_code` varchar(255) NULL COMMENT '测试结论编码' AFTER `material_remark`,
    ADD COLUMN `customer_conclusion` varchar(50) NULL COMMENT '客户测试结论' AFTER `conclusion_code`,
    ADD COLUMN `conclusion_remark` varchar(255) NULL COMMENT '测试结论备注' AFTER `customer_conclusion`,
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `last_modified_timestamp` datetime(0) NULL ON
UPDATE CURRENT_TIMESTAMP(0) COMMENT ' DB 自动更新，不允许程序设置 ' AFTER `rd_report_id`;


ALTER TABLE `tb_test_data_matrix_info_hl_nb`
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `ModifiedDate`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_matrix_group_id` int(11) NULL COMMENT '测试单位分组标识' AFTER `report_no`,
    ADD COLUMN `test_line_instance_id` varchar(64) NULL COMMENT '测试项实例标识' AFTER `test_matrix_group_id`,
    ADD COLUMN `evaluation_name` varchar(255) NULL COMMENT '测试项名称' AFTER `test_line_instance_id`,
    ADD COLUMN `test_line_status` varchar(255) NULL COMMENT '测试项状态' AFTER `evaluation_name`,
    ADD COLUMN `test_line_remark` varchar(255) NULL COMMENT '测试项备注' AFTER `test_line_status`,
    ADD COLUMN `citation_full_name` varchar(255) NULL COMMENT '测试标准拼接名称' AFTER `test_line_remark`,
    ADD COLUMN `sample_instance_id` varchar(36) NULL COMMENT '样品实例标识' AFTER `citation_full_name`,
    ADD COLUMN `sample_group` text NULL COMMENT '样品分组信息' AFTER `sample_instance_id`,
    ADD COLUMN `sample_parent_id` varchar(36) NULL COMMENT '样品父级标识' AFTER `sample_group`,
    ADD COLUMN `sample_type` varchar(255) NULL COMMENT '样品类型' AFTER `sample_parent_id`,
    ADD COLUMN `category` varchar(255) NULL COMMENT '样品分类' AFTER `sample_type`,
    ADD COLUMN `material_color` varchar(255) NULL COMMENT '物料颜色' AFTER `category`,
    ADD COLUMN `composition` varchar(255) NULL COMMENT '物料材质' AFTER `material_color`,
    ADD COLUMN `material_description` varchar(255) NULL COMMENT '物料描述' AFTER `composition`,
    ADD COLUMN `material_end_use` varchar(255) NULL COMMENT '物料用途' AFTER `material_description`,
    ADD COLUMN `applicable_flag` varchar(255) NULL COMMENT 'NC样品标识' AFTER `material_end_use`,
    ADD COLUMN `material_other_sample_info` varchar(255) NULL COMMENT '其他样品信息' AFTER `applicable_flag`,
    ADD COLUMN `material_remark` varchar(255) NULL COMMENT '样品备注信息' AFTER `material_other_sample_info`,
    ADD COLUMN `conclusion_code` varchar(255) NULL COMMENT '测试结论编码' AFTER `material_remark`,
    ADD COLUMN `customer_conclusion` varchar(50) NULL COMMENT '客户测试结论' AFTER `conclusion_code`,
    ADD COLUMN `conclusion_remark` varchar(255) NULL COMMENT '测试结论备注' AFTER `customer_conclusion`,
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `last_modified_timestamp` datetime(0) NULL ON
UPDATE CURRENT_TIMESTAMP(0) COMMENT ' DB 自动更新，不允许程序设置 ' AFTER `rd_report_id`;


ALTER TABLE `tb_test_data_matrix_info_hl_nj`
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `ModifiedDate`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_matrix_group_id` int(11) NULL COMMENT '测试单位分组标识' AFTER `report_no`,
    ADD COLUMN `test_line_instance_id` varchar(64) NULL COMMENT '测试项实例标识' AFTER `test_matrix_group_id`,
    ADD COLUMN `evaluation_name` varchar(255) NULL COMMENT '测试项名称' AFTER `test_line_instance_id`,
    ADD COLUMN `test_line_status` varchar(255) NULL COMMENT '测试项状态' AFTER `evaluation_name`,
    ADD COLUMN `test_line_remark` varchar(255) NULL COMMENT '测试项备注' AFTER `test_line_status`,
    ADD COLUMN `citation_full_name` varchar(255) NULL COMMENT '测试标准拼接名称' AFTER `test_line_remark`,
    ADD COLUMN `sample_instance_id` varchar(36) NULL COMMENT '样品实例标识' AFTER `citation_full_name`,
    ADD COLUMN `sample_group` text NULL COMMENT '样品分组信息' AFTER `sample_instance_id`,
    ADD COLUMN `sample_parent_id` varchar(36) NULL COMMENT '样品父级标识' AFTER `sample_group`,
    ADD COLUMN `sample_type` varchar(255) NULL COMMENT '样品类型' AFTER `sample_parent_id`,
    ADD COLUMN `category` varchar(255) NULL COMMENT '样品分类' AFTER `sample_type`,
    ADD COLUMN `material_color` varchar(255) NULL COMMENT '物料颜色' AFTER `category`,
    ADD COLUMN `composition` varchar(255) NULL COMMENT '物料材质' AFTER `material_color`,
    ADD COLUMN `material_description` varchar(255) NULL COMMENT '物料描述' AFTER `composition`,
    ADD COLUMN `material_end_use` varchar(255) NULL COMMENT '物料用途' AFTER `material_description`,
    ADD COLUMN `applicable_flag` varchar(255) NULL COMMENT 'NC样品标识' AFTER `material_end_use`,
    ADD COLUMN `material_other_sample_info` varchar(255) NULL COMMENT '其他样品信息' AFTER `applicable_flag`,
    ADD COLUMN `material_remark` varchar(255) NULL COMMENT '样品备注信息' AFTER `material_other_sample_info`,
    ADD COLUMN `conclusion_code` varchar(255) NULL COMMENT '测试结论编码' AFTER `material_remark`,
    ADD COLUMN `customer_conclusion` varchar(50) NULL COMMENT '客户测试结论' AFTER `conclusion_code`,
    ADD COLUMN `conclusion_remark` varchar(255) NULL COMMENT '测试结论备注' AFTER `customer_conclusion`,
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `last_modified_timestamp` datetime(0) NULL ON
UPDATE CURRENT_TIMESTAMP(0) COMMENT ' DB 自动更新，不允许程序设置 ' AFTER `rd_report_id`;


ALTER TABLE `tb_test_data_matrix_info_hl_qd`
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `ModifiedDate`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_matrix_group_id` int(11) NULL COMMENT '测试单位分组标识' AFTER `report_no`,
    ADD COLUMN `test_line_instance_id` varchar(64) NULL COMMENT '测试项实例标识' AFTER `test_matrix_group_id`,
    ADD COLUMN `evaluation_name` varchar(255) NULL COMMENT '测试项名称' AFTER `test_line_instance_id`,
    ADD COLUMN `test_line_status` varchar(255) NULL COMMENT '测试项状态' AFTER `evaluation_name`,
    ADD COLUMN `test_line_remark` varchar(255) NULL COMMENT '测试项备注' AFTER `test_line_status`,
    ADD COLUMN `citation_full_name` varchar(255) NULL COMMENT '测试标准拼接名称' AFTER `test_line_remark`,
    ADD COLUMN `sample_instance_id` varchar(36) NULL COMMENT '样品实例标识' AFTER `citation_full_name`,
    ADD COLUMN `sample_group` text NULL COMMENT '样品分组信息' AFTER `sample_instance_id`,
    ADD COLUMN `sample_parent_id` varchar(36) NULL COMMENT '样品父级标识' AFTER `sample_group`,
    ADD COLUMN `sample_type` varchar(255) NULL COMMENT '样品类型' AFTER `sample_parent_id`,
    ADD COLUMN `category` varchar(255) NULL COMMENT '样品分类' AFTER `sample_type`,
    ADD COLUMN `material_color` varchar(255) NULL COMMENT '物料颜色' AFTER `category`,
    ADD COLUMN `composition` varchar(255) NULL COMMENT '物料材质' AFTER `material_color`,
    ADD COLUMN `material_description` varchar(255) NULL COMMENT '物料描述' AFTER `composition`,
    ADD COLUMN `material_end_use` varchar(255) NULL COMMENT '物料用途' AFTER `material_description`,
    ADD COLUMN `applicable_flag` varchar(255) NULL COMMENT 'NC样品标识' AFTER `material_end_use`,
    ADD COLUMN `material_other_sample_info` varchar(255) NULL COMMENT '其他样品信息' AFTER `applicable_flag`,
    ADD COLUMN `material_remark` varchar(255) NULL COMMENT '样品备注信息' AFTER `material_other_sample_info`,
    ADD COLUMN `conclusion_code` varchar(255) NULL COMMENT '测试结论编码' AFTER `material_remark`,
    ADD COLUMN `customer_conclusion` varchar(50) NULL COMMENT '客户测试结论' AFTER `conclusion_code`,
    ADD COLUMN `conclusion_remark` varchar(255) NULL COMMENT '测试结论备注' AFTER `customer_conclusion`,
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `last_modified_timestamp` datetime(0) NULL ON
UPDATE CURRENT_TIMESTAMP(0) COMMENT ' DB 自动更新，不允许程序设置 ' AFTER `rd_report_id`;


ALTER TABLE `tb_test_data_matrix_info_hl_sd`
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `ModifiedDate`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_matrix_group_id` int(11) NULL COMMENT '测试单位分组标识' AFTER `report_no`,
    ADD COLUMN `test_line_instance_id` varchar(64) NULL COMMENT '测试项实例标识' AFTER `test_matrix_group_id`,
    ADD COLUMN `evaluation_name` varchar(255) NULL COMMENT '测试项名称' AFTER `test_line_instance_id`,
    ADD COLUMN `test_line_status` varchar(255) NULL COMMENT '测试项状态' AFTER `evaluation_name`,
    ADD COLUMN `test_line_remark` varchar(255) NULL COMMENT '测试项备注' AFTER `test_line_status`,
    ADD COLUMN `citation_full_name` varchar(255) NULL COMMENT '测试标准拼接名称' AFTER `test_line_remark`,
    ADD COLUMN `sample_instance_id` varchar(36) NULL COMMENT '样品实例标识' AFTER `citation_full_name`,
    ADD COLUMN `sample_group` text NULL COMMENT '样品分组信息' AFTER `sample_instance_id`,
    ADD COLUMN `sample_parent_id` varchar(36) NULL COMMENT '样品父级标识' AFTER `sample_group`,
    ADD COLUMN `sample_type` varchar(255) NULL COMMENT '样品类型' AFTER `sample_parent_id`,
    ADD COLUMN `category` varchar(255) NULL COMMENT '样品分类' AFTER `sample_type`,
    ADD COLUMN `material_color` varchar(255) NULL COMMENT '物料颜色' AFTER `category`,
    ADD COLUMN `composition` varchar(255) NULL COMMENT '物料材质' AFTER `material_color`,
    ADD COLUMN `material_description` varchar(255) NULL COMMENT '物料描述' AFTER `composition`,
    ADD COLUMN `material_end_use` varchar(255) NULL COMMENT '物料用途' AFTER `material_description`,
    ADD COLUMN `applicable_flag` varchar(255) NULL COMMENT 'NC样品标识' AFTER `material_end_use`,
    ADD COLUMN `material_other_sample_info` varchar(255) NULL COMMENT '其他样品信息' AFTER `applicable_flag`,
    ADD COLUMN `material_remark` varchar(255) NULL COMMENT '样品备注信息' AFTER `material_other_sample_info`,
    ADD COLUMN `conclusion_code` varchar(255) NULL COMMENT '测试结论编码' AFTER `material_remark`,
    ADD COLUMN `customer_conclusion` varchar(50) NULL COMMENT '客户测试结论' AFTER `conclusion_code`,
    ADD COLUMN `conclusion_remark` varchar(255) NULL COMMENT '测试结论备注' AFTER `customer_conclusion`,
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `last_modified_timestamp` datetime(0) NULL ON
UPDATE CURRENT_TIMESTAMP(0) COMMENT ' DB 自动更新，不允许程序设置 ' AFTER `rd_report_id`;


ALTER TABLE `tb_test_data_matrix_info_hl_sh`
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `ModifiedDate`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_matrix_group_id` int(11) NULL COMMENT '测试单位分组标识' AFTER `report_no`,
    ADD COLUMN `test_line_instance_id` varchar(64) NULL COMMENT '测试项实例标识' AFTER `test_matrix_group_id`,
    ADD COLUMN `evaluation_name` varchar(255) NULL COMMENT '测试项名称' AFTER `test_line_instance_id`,
    ADD COLUMN `test_line_status` varchar(255) NULL COMMENT '测试项状态' AFTER `evaluation_name`,
    ADD COLUMN `test_line_remark` varchar(255) NULL COMMENT '测试项备注' AFTER `test_line_status`,
    ADD COLUMN `citation_full_name` varchar(255) NULL COMMENT '测试标准拼接名称' AFTER `test_line_remark`,
    ADD COLUMN `sample_instance_id` varchar(36) NULL COMMENT '样品实例标识' AFTER `citation_full_name`,
    ADD COLUMN `sample_group` text NULL COMMENT '样品分组信息' AFTER `sample_instance_id`,
    ADD COLUMN `sample_parent_id` varchar(36) NULL COMMENT '样品父级标识' AFTER `sample_group`,
    ADD COLUMN `sample_type` varchar(255) NULL COMMENT '样品类型' AFTER `sample_parent_id`,
    ADD COLUMN `category` varchar(255) NULL COMMENT '样品分类' AFTER `sample_type`,
    ADD COLUMN `material_color` varchar(255) NULL COMMENT '物料颜色' AFTER `category`,
    ADD COLUMN `composition` varchar(255) NULL COMMENT '物料材质' AFTER `material_color`,
    ADD COLUMN `material_description` varchar(255) NULL COMMENT '物料描述' AFTER `composition`,
    ADD COLUMN `material_end_use` varchar(255) NULL COMMENT '物料用途' AFTER `material_description`,
    ADD COLUMN `applicable_flag` varchar(255) NULL COMMENT 'NC样品标识' AFTER `material_end_use`,
    ADD COLUMN `material_other_sample_info` varchar(255) NULL COMMENT '其他样品信息' AFTER `applicable_flag`,
    ADD COLUMN `material_remark` varchar(255) NULL COMMENT '样品备注信息' AFTER `material_other_sample_info`,
    ADD COLUMN `conclusion_code` varchar(255) NULL COMMENT '测试结论编码' AFTER `material_remark`,
    ADD COLUMN `customer_conclusion` varchar(50) NULL COMMENT '客户测试结论' AFTER `conclusion_code`,
    ADD COLUMN `conclusion_remark` varchar(255) NULL COMMENT '测试结论备注' AFTER `customer_conclusion`,
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `last_modified_timestamp` datetime(0) NULL ON
UPDATE CURRENT_TIMESTAMP(0) COMMENT ' DB 自动更新，不允许程序设置 ' AFTER `rd_report_id`;



ALTER TABLE `tb_test_data_matrix_info_hl_sz`
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `ModifiedDate`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_matrix_group_id` int(11) NULL COMMENT '测试单位分组标识' AFTER `report_no`,
    ADD COLUMN `test_line_instance_id` varchar(64) NULL COMMENT '测试项实例标识' AFTER `test_matrix_group_id`,
    ADD COLUMN `evaluation_name` varchar(255) NULL COMMENT '测试项名称' AFTER `test_line_instance_id`,
    ADD COLUMN `test_line_status` varchar(255) NULL COMMENT '测试项状态' AFTER `evaluation_name`,
    ADD COLUMN `test_line_remark` varchar(255) NULL COMMENT '测试项备注' AFTER `test_line_status`,
    ADD COLUMN `citation_full_name` varchar(255) NULL COMMENT '测试标准拼接名称' AFTER `test_line_remark`,
    ADD COLUMN `sample_instance_id` varchar(36) NULL COMMENT '样品实例标识' AFTER `citation_full_name`,
    ADD COLUMN `sample_group` text NULL COMMENT '样品分组信息' AFTER `sample_instance_id`,
    ADD COLUMN `sample_parent_id` varchar(36) NULL COMMENT '样品父级标识' AFTER `sample_group`,
    ADD COLUMN `sample_type` varchar(255) NULL COMMENT '样品类型' AFTER `sample_parent_id`,
    ADD COLUMN `category` varchar(255) NULL COMMENT '样品分类' AFTER `sample_type`,
    ADD COLUMN `material_color` varchar(255) NULL COMMENT '物料颜色' AFTER `category`,
    ADD COLUMN `composition` varchar(255) NULL COMMENT '物料材质' AFTER `material_color`,
    ADD COLUMN `material_description` varchar(255) NULL COMMENT '物料描述' AFTER `composition`,
    ADD COLUMN `material_end_use` varchar(255) NULL COMMENT '物料用途' AFTER `material_description`,
    ADD COLUMN `applicable_flag` varchar(255) NULL COMMENT 'NC样品标识' AFTER `material_end_use`,
    ADD COLUMN `material_other_sample_info` varchar(255) NULL COMMENT '其他样品信息' AFTER `applicable_flag`,
    ADD COLUMN `material_remark` varchar(255) NULL COMMENT '样品备注信息' AFTER `material_other_sample_info`,
    ADD COLUMN `conclusion_code` varchar(255) NULL COMMENT '测试结论编码' AFTER `material_remark`,
    ADD COLUMN `customer_conclusion` varchar(50) NULL COMMENT '客户测试结论' AFTER `conclusion_code`,
    ADD COLUMN `conclusion_remark` varchar(255) NULL COMMENT '测试结论备注' AFTER `customer_conclusion`,
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `last_modified_timestamp` datetime(0) NULL ON
UPDATE CURRENT_TIMESTAMP(0) COMMENT ' DB 自动更新，不允许程序设置 ' AFTER `rd_report_id`;



ALTER TABLE `tb_test_data_matrix_info_hl_tc`
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `ModifiedDate`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_matrix_group_id` int(11) NULL COMMENT '测试单位分组标识' AFTER `report_no`,
    ADD COLUMN `test_line_instance_id` varchar(64) NULL COMMENT '测试项实例标识' AFTER `test_matrix_group_id`,
    ADD COLUMN `evaluation_name` varchar(255) NULL COMMENT '测试项名称' AFTER `test_line_instance_id`,
    ADD COLUMN `test_line_status` varchar(255) NULL COMMENT '测试项状态' AFTER `evaluation_name`,
    ADD COLUMN `test_line_remark` varchar(255) NULL COMMENT '测试项备注' AFTER `test_line_status`,
    ADD COLUMN `citation_full_name` varchar(255) NULL COMMENT '测试标准拼接名称' AFTER `test_line_remark`,
    ADD COLUMN `sample_instance_id` varchar(36) NULL COMMENT '样品实例标识' AFTER `citation_full_name`,
    ADD COLUMN `sample_group` text NULL COMMENT '样品分组信息' AFTER `sample_instance_id`,
    ADD COLUMN `sample_parent_id` varchar(36) NULL COMMENT '样品父级标识' AFTER `sample_group`,
    ADD COLUMN `sample_type` varchar(255) NULL COMMENT '样品类型' AFTER `sample_parent_id`,
    ADD COLUMN `category` varchar(255) NULL COMMENT '样品分类' AFTER `sample_type`,
    ADD COLUMN `material_color` varchar(255) NULL COMMENT '物料颜色' AFTER `category`,
    ADD COLUMN `composition` varchar(255) NULL COMMENT '物料材质' AFTER `material_color`,
    ADD COLUMN `material_description` varchar(255) NULL COMMENT '物料描述' AFTER `composition`,
    ADD COLUMN `material_end_use` varchar(255) NULL COMMENT '物料用途' AFTER `material_description`,
    ADD COLUMN `applicable_flag` varchar(255) NULL COMMENT 'NC样品标识' AFTER `material_end_use`,
    ADD COLUMN `material_other_sample_info` varchar(255) NULL COMMENT '其他样品信息' AFTER `applicable_flag`,
    ADD COLUMN `material_remark` varchar(255) NULL COMMENT '样品备注信息' AFTER `material_other_sample_info`,
    ADD COLUMN `conclusion_code` varchar(255) NULL COMMENT '测试结论编码' AFTER `material_remark`,
    ADD COLUMN `customer_conclusion` varchar(50) NULL COMMENT '客户测试结论' AFTER `conclusion_code`,
    ADD COLUMN `conclusion_remark` varchar(255) NULL COMMENT '测试结论备注' AFTER `customer_conclusion`,
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `last_modified_timestamp` datetime(0) NULL ON
UPDATE CURRENT_TIMESTAMP(0) COMMENT ' DB 自动更新，不允许程序设置 ' AFTER `rd_report_id`;


ALTER TABLE `tb_test_data_matrix_info_hl_tj`
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `ModifiedDate`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_matrix_group_id` int(11) NULL COMMENT '测试单位分组标识' AFTER `report_no`,
    ADD COLUMN `test_line_instance_id` varchar(64) NULL COMMENT '测试项实例标识' AFTER `test_matrix_group_id`,
    ADD COLUMN `evaluation_name` varchar(255) NULL COMMENT '测试项名称' AFTER `test_line_instance_id`,
    ADD COLUMN `test_line_status` varchar(255) NULL COMMENT '测试项状态' AFTER `evaluation_name`,
    ADD COLUMN `test_line_remark` varchar(255) NULL COMMENT '测试项备注' AFTER `test_line_status`,
    ADD COLUMN `citation_full_name` varchar(255) NULL COMMENT '测试标准拼接名称' AFTER `test_line_remark`,
    ADD COLUMN `sample_instance_id` varchar(36) NULL COMMENT '样品实例标识' AFTER `citation_full_name`,
    ADD COLUMN `sample_group` text NULL COMMENT '样品分组信息' AFTER `sample_instance_id`,
    ADD COLUMN `sample_parent_id` varchar(36) NULL COMMENT '样品父级标识' AFTER `sample_group`,
    ADD COLUMN `sample_type` varchar(255) NULL COMMENT '样品类型' AFTER `sample_parent_id`,
    ADD COLUMN `category` varchar(255) NULL COMMENT '样品分类' AFTER `sample_type`,
    ADD COLUMN `material_color` varchar(255) NULL COMMENT '物料颜色' AFTER `category`,
    ADD COLUMN `composition` varchar(255) NULL COMMENT '物料材质' AFTER `material_color`,
    ADD COLUMN `material_description` varchar(255) NULL COMMENT '物料描述' AFTER `composition`,
    ADD COLUMN `material_end_use` varchar(255) NULL COMMENT '物料用途' AFTER `material_description`,
    ADD COLUMN `applicable_flag` varchar(255) NULL COMMENT 'NC样品标识' AFTER `material_end_use`,
    ADD COLUMN `material_other_sample_info` varchar(255) NULL COMMENT '其他样品信息' AFTER `applicable_flag`,
    ADD COLUMN `material_remark` varchar(255) NULL COMMENT '样品备注信息' AFTER `material_other_sample_info`,
    ADD COLUMN `conclusion_code` varchar(255) NULL COMMENT '测试结论编码' AFTER `material_remark`,
    ADD COLUMN `customer_conclusion` varchar(50) NULL COMMENT '客户测试结论' AFTER `conclusion_code`,
    ADD COLUMN `conclusion_remark` varchar(255) NULL COMMENT '测试结论备注' AFTER `customer_conclusion`,
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `last_modified_timestamp` datetime(0) NULL ON
UPDATE CURRENT_TIMESTAMP(0) COMMENT ' DB 自动更新，不允许程序设置 ' AFTER `rd_report_id`;

ALTER TABLE `tb_test_data_matrix_info_hl_tp`
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `ModifiedDate`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_matrix_group_id` int(11) NULL COMMENT '测试单位分组标识' AFTER `report_no`,
    ADD COLUMN `test_line_instance_id` varchar(64) NULL COMMENT '测试项实例标识' AFTER `test_matrix_group_id`,
    ADD COLUMN `evaluation_name` varchar(255) NULL COMMENT '测试项名称' AFTER `test_line_instance_id`,
    ADD COLUMN `test_line_status` varchar(255) NULL COMMENT '测试项状态' AFTER `evaluation_name`,
    ADD COLUMN `test_line_remark` varchar(255) NULL COMMENT '测试项备注' AFTER `test_line_status`,
    ADD COLUMN `citation_full_name` varchar(255) NULL COMMENT '测试标准拼接名称' AFTER `test_line_remark`,
    ADD COLUMN `sample_instance_id` varchar(36) NULL COMMENT '样品实例标识' AFTER `citation_full_name`,
    ADD COLUMN `sample_group` text NULL COMMENT '样品分组信息' AFTER `sample_instance_id`,
    ADD COLUMN `sample_parent_id` varchar(36) NULL COMMENT '样品父级标识' AFTER `sample_group`,
    ADD COLUMN `sample_type` varchar(255) NULL COMMENT '样品类型' AFTER `sample_parent_id`,
    ADD COLUMN `category` varchar(255) NULL COMMENT '样品分类' AFTER `sample_type`,
    ADD COLUMN `material_color` varchar(255) NULL COMMENT '物料颜色' AFTER `category`,
    ADD COLUMN `composition` varchar(255) NULL COMMENT '物料材质' AFTER `material_color`,
    ADD COLUMN `material_description` varchar(255) NULL COMMENT '物料描述' AFTER `composition`,
    ADD COLUMN `material_end_use` varchar(255) NULL COMMENT '物料用途' AFTER `material_description`,
    ADD COLUMN `applicable_flag` varchar(255) NULL COMMENT 'NC样品标识' AFTER `material_end_use`,
    ADD COLUMN `material_other_sample_info` varchar(255) NULL COMMENT '其他样品信息' AFTER `applicable_flag`,
    ADD COLUMN `material_remark` varchar(255) NULL COMMENT '样品备注信息' AFTER `material_other_sample_info`,
    ADD COLUMN `conclusion_code` varchar(255) NULL COMMENT '测试结论编码' AFTER `material_remark`,
    ADD COLUMN `customer_conclusion` varchar(50) NULL COMMENT '客户测试结论' AFTER `conclusion_code`,
    ADD COLUMN `conclusion_remark` varchar(255) NULL COMMENT '测试结论备注' AFTER `customer_conclusion`,
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `last_modified_timestamp` datetime(0) NULL ON
UPDATE CURRENT_TIMESTAMP(0) COMMENT ' DB 自动更新，不允许程序设置 ' AFTER `rd_report_id`;


ALTER TABLE `tb_test_data_matrix_info_hl_xm`
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `ModifiedDate`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_matrix_group_id` int(11) NULL COMMENT '测试单位分组标识' AFTER `report_no`,
    ADD COLUMN `test_line_instance_id` varchar(64) NULL COMMENT '测试项实例标识' AFTER `test_matrix_group_id`,
    ADD COLUMN `evaluation_name` varchar(255) NULL COMMENT '测试项名称' AFTER `test_line_instance_id`,
    ADD COLUMN `test_line_status` varchar(255) NULL COMMENT '测试项状态' AFTER `evaluation_name`,
    ADD COLUMN `test_line_remark` varchar(255) NULL COMMENT '测试项备注' AFTER `test_line_status`,
    ADD COLUMN `citation_full_name` varchar(255) NULL COMMENT '测试标准拼接名称' AFTER `test_line_remark`,
    ADD COLUMN `sample_instance_id` varchar(36) NULL COMMENT '样品实例标识' AFTER `citation_full_name`,
    ADD COLUMN `sample_group` text NULL COMMENT '样品分组信息' AFTER `sample_instance_id`,
    ADD COLUMN `sample_parent_id` varchar(36) NULL COMMENT '样品父级标识' AFTER `sample_group`,
    ADD COLUMN `sample_type` varchar(255) NULL COMMENT '样品类型' AFTER `sample_parent_id`,
    ADD COLUMN `category` varchar(255) NULL COMMENT '样品分类' AFTER `sample_type`,
    ADD COLUMN `material_color` varchar(255) NULL COMMENT '物料颜色' AFTER `category`,
    ADD COLUMN `composition` varchar(255) NULL COMMENT '物料材质' AFTER `material_color`,
    ADD COLUMN `material_description` varchar(255) NULL COMMENT '物料描述' AFTER `composition`,
    ADD COLUMN `material_end_use` varchar(255) NULL COMMENT '物料用途' AFTER `material_description`,
    ADD COLUMN `applicable_flag` varchar(255) NULL COMMENT 'NC样品标识' AFTER `material_end_use`,
    ADD COLUMN `material_other_sample_info` varchar(255) NULL COMMENT '其他样品信息' AFTER `applicable_flag`,
    ADD COLUMN `material_remark` varchar(255) NULL COMMENT '样品备注信息' AFTER `material_other_sample_info`,
    ADD COLUMN `conclusion_code` varchar(255) NULL COMMENT '测试结论编码' AFTER `material_remark`,
    ADD COLUMN `customer_conclusion` varchar(50) NULL COMMENT '客户测试结论' AFTER `conclusion_code`,
    ADD COLUMN `conclusion_remark` varchar(255) NULL COMMENT '测试结论备注' AFTER `customer_conclusion`,
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `last_modified_timestamp` datetime(0) NULL ON
UPDATE CURRENT_TIMESTAMP(0) COMMENT ' DB 自动更新，不允许程序设置 ' AFTER `rd_report_id`;


ALTER TABLE `tb_test_data_matrix_info_sl_cz`
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `ModifiedDate`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_matrix_group_id` int(11) NULL COMMENT '测试单位分组标识' AFTER `report_no`,
    ADD COLUMN `test_line_instance_id` varchar(64) NULL COMMENT '测试项实例标识' AFTER `test_matrix_group_id`,
    ADD COLUMN `evaluation_name` varchar(255) NULL COMMENT '测试项名称' AFTER `test_line_instance_id`,
    ADD COLUMN `test_line_status` varchar(255) NULL COMMENT '测试项状态' AFTER `evaluation_name`,
    ADD COLUMN `test_line_remark` varchar(255) NULL COMMENT '测试项备注' AFTER `test_line_status`,
    ADD COLUMN `citation_full_name` varchar(255) NULL COMMENT '测试标准拼接名称' AFTER `test_line_remark`,
    ADD COLUMN `sample_instance_id` varchar(36) NULL COMMENT '样品实例标识' AFTER `citation_full_name`,
    ADD COLUMN `sample_group` text NULL COMMENT '样品分组信息' AFTER `sample_instance_id`,
    ADD COLUMN `sample_parent_id` varchar(36) NULL COMMENT '样品父级标识' AFTER `sample_group`,
    ADD COLUMN `sample_type` varchar(255) NULL COMMENT '样品类型' AFTER `sample_parent_id`,
    ADD COLUMN `category` varchar(255) NULL COMMENT '样品分类' AFTER `sample_type`,
    ADD COLUMN `material_color` varchar(255) NULL COMMENT '物料颜色' AFTER `category`,
    ADD COLUMN `composition` varchar(255) NULL COMMENT '物料材质' AFTER `material_color`,
    ADD COLUMN `material_description` varchar(255) NULL COMMENT '物料描述' AFTER `composition`,
    ADD COLUMN `material_end_use` varchar(255) NULL COMMENT '物料用途' AFTER `material_description`,
    ADD COLUMN `applicable_flag` varchar(255) NULL COMMENT 'NC样品标识' AFTER `material_end_use`,
    ADD COLUMN `material_other_sample_info` varchar(255) NULL COMMENT '其他样品信息' AFTER `applicable_flag`,
    ADD COLUMN `material_remark` varchar(255) NULL COMMENT '样品备注信息' AFTER `material_other_sample_info`,
    ADD COLUMN `conclusion_code` varchar(255) NULL COMMENT '测试结论编码' AFTER `material_remark`,
    ADD COLUMN `customer_conclusion` varchar(50) NULL COMMENT '客户测试结论' AFTER `conclusion_code`,
    ADD COLUMN `conclusion_remark` varchar(255) NULL COMMENT '测试结论备注' AFTER `customer_conclusion`,
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `last_modified_timestamp` datetime(0) NULL ON
UPDATE CURRENT_TIMESTAMP(0) COMMENT ' DB 自动更新，不允许程序设置 ' AFTER `rd_report_id`;


ALTER TABLE `tb_test_data_matrix_info_sl_gz`
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `ModifiedDate`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_matrix_group_id` int(11) NULL COMMENT '测试单位分组标识' AFTER `report_no`,
    ADD COLUMN `test_line_instance_id` varchar(64) NULL COMMENT '测试项实例标识' AFTER `test_matrix_group_id`,
    ADD COLUMN `evaluation_name` varchar(255) NULL COMMENT '测试项名称' AFTER `test_line_instance_id`,
    ADD COLUMN `test_line_status` varchar(255) NULL COMMENT '测试项状态' AFTER `evaluation_name`,
    ADD COLUMN `test_line_remark` varchar(255) NULL COMMENT '测试项备注' AFTER `test_line_status`,
    ADD COLUMN `citation_full_name` varchar(255) NULL COMMENT '测试标准拼接名称' AFTER `test_line_remark`,
    ADD COLUMN `sample_instance_id` varchar(36) NULL COMMENT '样品实例标识' AFTER `citation_full_name`,
    ADD COLUMN `sample_group` text NULL COMMENT '样品分组信息' AFTER `sample_instance_id`,
    ADD COLUMN `sample_parent_id` varchar(36) NULL COMMENT '样品父级标识' AFTER `sample_group`,
    ADD COLUMN `sample_type` varchar(255) NULL COMMENT '样品类型' AFTER `sample_parent_id`,
    ADD COLUMN `category` varchar(255) NULL COMMENT '样品分类' AFTER `sample_type`,
    ADD COLUMN `material_color` varchar(255) NULL COMMENT '物料颜色' AFTER `category`,
    ADD COLUMN `composition` varchar(255) NULL COMMENT '物料材质' AFTER `material_color`,
    ADD COLUMN `material_description` varchar(255) NULL COMMENT '物料描述' AFTER `composition`,
    ADD COLUMN `material_end_use` varchar(255) NULL COMMENT '物料用途' AFTER `material_description`,
    ADD COLUMN `applicable_flag` varchar(255) NULL COMMENT 'NC样品标识' AFTER `material_end_use`,
    ADD COLUMN `material_other_sample_info` varchar(255) NULL COMMENT '其他样品信息' AFTER `applicable_flag`,
    ADD COLUMN `material_remark` varchar(255) NULL COMMENT '样品备注信息' AFTER `material_other_sample_info`,
    ADD COLUMN `conclusion_code` varchar(255) NULL COMMENT '测试结论编码' AFTER `material_remark`,
    ADD COLUMN `customer_conclusion` varchar(50) NULL COMMENT '客户测试结论' AFTER `conclusion_code`,
    ADD COLUMN `conclusion_remark` varchar(255) NULL COMMENT '测试结论备注' AFTER `customer_conclusion`,
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `last_modified_timestamp` datetime(0) NULL ON
UPDATE CURRENT_TIMESTAMP(0) COMMENT ' DB 自动更新，不允许程序设置 ' AFTER `rd_report_id`;


ALTER TABLE `tb_test_data_matrix_info_sl_hk`
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `ModifiedDate`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_matrix_group_id` int(11) NULL COMMENT '测试单位分组标识' AFTER `report_no`,
    ADD COLUMN `test_line_instance_id` varchar(64) NULL COMMENT '测试项实例标识' AFTER `test_matrix_group_id`,
    ADD COLUMN `evaluation_name` varchar(255) NULL COMMENT '测试项名称' AFTER `test_line_instance_id`,
    ADD COLUMN `test_line_status` varchar(255) NULL COMMENT '测试项状态' AFTER `evaluation_name`,
    ADD COLUMN `test_line_remark` varchar(255) NULL COMMENT '测试项备注' AFTER `test_line_status`,
    ADD COLUMN `citation_full_name` varchar(255) NULL COMMENT '测试标准拼接名称' AFTER `test_line_remark`,
    ADD COLUMN `sample_instance_id` varchar(36) NULL COMMENT '样品实例标识' AFTER `citation_full_name`,
    ADD COLUMN `sample_group` text NULL COMMENT '样品分组信息' AFTER `sample_instance_id`,
    ADD COLUMN `sample_parent_id` varchar(36) NULL COMMENT '样品父级标识' AFTER `sample_group`,
    ADD COLUMN `sample_type` varchar(255) NULL COMMENT '样品类型' AFTER `sample_parent_id`,
    ADD COLUMN `category` varchar(255) NULL COMMENT '样品分类' AFTER `sample_type`,
    ADD COLUMN `material_color` varchar(255) NULL COMMENT '物料颜色' AFTER `category`,
    ADD COLUMN `composition` varchar(255) NULL COMMENT '物料材质' AFTER `material_color`,
    ADD COLUMN `material_description` varchar(255) NULL COMMENT '物料描述' AFTER `composition`,
    ADD COLUMN `material_end_use` varchar(255) NULL COMMENT '物料用途' AFTER `material_description`,
    ADD COLUMN `applicable_flag` varchar(255) NULL COMMENT 'NC样品标识' AFTER `material_end_use`,
    ADD COLUMN `material_other_sample_info` varchar(255) NULL COMMENT '其他样品信息' AFTER `applicable_flag`,
    ADD COLUMN `material_remark` varchar(255) NULL COMMENT '样品备注信息' AFTER `material_other_sample_info`,
    ADD COLUMN `conclusion_code` varchar(255) NULL COMMENT '测试结论编码' AFTER `material_remark`,
    ADD COLUMN `customer_conclusion` varchar(50) NULL COMMENT '客户测试结论' AFTER `conclusion_code`,
    ADD COLUMN `conclusion_remark` varchar(255) NULL COMMENT '测试结论备注' AFTER `customer_conclusion`,
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `last_modified_timestamp` datetime(0) NULL ON
UPDATE CURRENT_TIMESTAMP(0) COMMENT ' DB 自动更新，不允许程序设置 ' AFTER `rd_report_id`;



ALTER TABLE `tb_test_data_matrix_info_sl_hz`
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `ModifiedDate`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_matrix_group_id` int(11) NULL COMMENT '测试单位分组标识' AFTER `report_no`,
    ADD COLUMN `test_line_instance_id` varchar(64) NULL COMMENT '测试项实例标识' AFTER `test_matrix_group_id`,
    ADD COLUMN `evaluation_name` varchar(255) NULL COMMENT '测试项名称' AFTER `test_line_instance_id`,
    ADD COLUMN `test_line_status` varchar(255) NULL COMMENT '测试项状态' AFTER `evaluation_name`,
    ADD COLUMN `test_line_remark` varchar(255) NULL COMMENT '测试项备注' AFTER `test_line_status`,
    ADD COLUMN `citation_full_name` varchar(255) NULL COMMENT '测试标准拼接名称' AFTER `test_line_remark`,
    ADD COLUMN `sample_instance_id` varchar(36) NULL COMMENT '样品实例标识' AFTER `citation_full_name`,
    ADD COLUMN `sample_group` text NULL COMMENT '样品分组信息' AFTER `sample_instance_id`,
    ADD COLUMN `sample_parent_id` varchar(36) NULL COMMENT '样品父级标识' AFTER `sample_group`,
    ADD COLUMN `sample_type` varchar(255) NULL COMMENT '样品类型' AFTER `sample_parent_id`,
    ADD COLUMN `category` varchar(255) NULL COMMENT '样品分类' AFTER `sample_type`,
    ADD COLUMN `material_color` varchar(255) NULL COMMENT '物料颜色' AFTER `category`,
    ADD COLUMN `composition` varchar(255) NULL COMMENT '物料材质' AFTER `material_color`,
    ADD COLUMN `material_description` varchar(255) NULL COMMENT '物料描述' AFTER `composition`,
    ADD COLUMN `material_end_use` varchar(255) NULL COMMENT '物料用途' AFTER `material_description`,
    ADD COLUMN `applicable_flag` varchar(255) NULL COMMENT 'NC样品标识' AFTER `material_end_use`,
    ADD COLUMN `material_other_sample_info` varchar(255) NULL COMMENT '其他样品信息' AFTER `applicable_flag`,
    ADD COLUMN `material_remark` varchar(255) NULL COMMENT '样品备注信息' AFTER `material_other_sample_info`,
    ADD COLUMN `conclusion_code` varchar(255) NULL COMMENT '测试结论编码' AFTER `material_remark`,
    ADD COLUMN `customer_conclusion` varchar(50) NULL COMMENT '客户测试结论' AFTER `conclusion_code`,
    ADD COLUMN `conclusion_remark` varchar(255) NULL COMMENT '测试结论备注' AFTER `customer_conclusion`,
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `last_modified_timestamp` datetime(0) NULL ON
UPDATE CURRENT_TIMESTAMP(0) COMMENT ' DB 自动更新，不允许程序设置 ' AFTER `rd_report_id`;



CREATE TABLE `tb_test_data_matrix_info_sl_isb` (
                                                   `id` bigint(20) NOT NULL,
                                                   `ObjectRelId` varchar(36) DEFAULT NULL,
                                                   `TestMatrixId` varchar(36) DEFAULT NULL,
                                                   `TestLineMappingId` int(11) DEFAULT NULL,
                                                   `ExternalId` varchar(300) DEFAULT NULL,
                                                   `ExternalCode` varchar(50) DEFAULT NULL,
                                                   `PpVersionId` int(11) DEFAULT NULL,
                                                   `Aid` bigint(20) DEFAULT NULL,
                                                   `TestLineId` int(11) DEFAULT NULL,
                                                   `CitationId` int(11) DEFAULT NULL,
                                                   `CitationVersionId` int(11) DEFAULT NULL,
                                                   `CitationType` int(11) DEFAULT '0' COMMENT '0：None、1：Method、2：Regulation、3：Standard',
                                                   `CitationName` varchar(1000) DEFAULT NULL,
                                                   `SampleId` varchar(36) DEFAULT NULL,
                                                   `SampleNo` varchar(255) DEFAULT NULL COMMENT '样品编号',
                                                   `ExternalSampleNo` varchar(500) DEFAULT NULL,
                                                   `TestLineSeq` bigint(20) DEFAULT NULL,
                                                   `SampleSeq` varchar(36) DEFAULT NULL,
                                                   `ExtFields` mediumtext,
                                                   `Condition` mediumtext,
                                                   `EvaluationAlias` varchar(500) DEFAULT NULL,
                                                   `MethodDesc` varchar(512) DEFAULT NULL,
                                                   `ConclusionId` varchar(50) DEFAULT NULL,
                                                   `ConclusionDisplay` varchar(50) DEFAULT NULL,
                                                   `Languages` mediumtext,
                                                   `BizVersionId` char(32) NOT NULL,
                                                   `ActiveIndicator` int(1) NOT NULL COMMENT '0无效，1有效',
                                                   `CreatedBy` varchar(50) DEFAULT NULL,
                                                   `CreatedDate` datetime DEFAULT NULL,
                                                   `ModifiedBy` varchar(50) CHARACTER SET utf8 DEFAULT NULL,
                                                   `ModifiedDate` datetime DEFAULT NULL,
                                                   `lab_id` bigint(20) DEFAULT NULL COMMENT 'Trims系统实验室标识',
                                                   `order_no` varchar(50) DEFAULT NULL COMMENT '订单号',
                                                   `report_no` varchar(50) DEFAULT NULL COMMENT '报告号',
                                                   `test_matrix_group_id` int(11) DEFAULT NULL COMMENT '测试单位分组标识',
                                                   `test_line_instance_id` varchar(64) DEFAULT NULL COMMENT '测试项实例标识',
                                                   `evaluation_name` varchar(255) DEFAULT NULL COMMENT '测试项名称',
                                                   `test_line_status` varchar(255) DEFAULT NULL COMMENT '测试项状态',
                                                   `test_line_remark` varchar(255) DEFAULT NULL COMMENT '测试项备注',
                                                   `citation_full_name` varchar(255) DEFAULT NULL COMMENT '测试标准拼接名称',
                                                   `sample_instance_id` varchar(36) DEFAULT NULL COMMENT '样品实例标识',
                                                   `sample_group` text COMMENT '样品分组信息',
                                                   `sample_parent_id` varchar(36) DEFAULT NULL COMMENT '样品父级标识',
                                                   `sample_type` varchar(255) DEFAULT NULL COMMENT '样品类型',
                                                   `category` varchar(255) DEFAULT NULL COMMENT '样品分类',
                                                   `material_color` varchar(255) DEFAULT NULL COMMENT '物料颜色',
                                                   `composition` varchar(255) DEFAULT NULL COMMENT '物料材质',
                                                   `material_description` varchar(255) DEFAULT NULL COMMENT '物料描述',
                                                   `material_end_use` varchar(255) DEFAULT NULL COMMENT '物料用途',
                                                   `applicable_flag` varchar(255) DEFAULT NULL COMMENT 'NC样品标识',
                                                   `material_other_sample_info` varchar(255) DEFAULT NULL COMMENT '其他样品信息',
                                                   `material_remark` varchar(255) DEFAULT NULL COMMENT '样品备注信息',
                                                   `conclusion_code` varchar(255) DEFAULT NULL COMMENT '测试结论编码',
                                                   `customer_conclusion` varchar(50) DEFAULT NULL COMMENT '客户测试结论',
                                                   `conclusion_remark` varchar(255) DEFAULT NULL COMMENT '测试结论备注',
                                                   `last_modified_timestamp` datetime DEFAULT NULL COMMENT 'DB 自动更新，不允许程序设置',
                                                   `rd_report_id` bigint(20) DEFAULT NULL,
                                                   PRIMARY KEY (`id`) USING BTREE,
                                                   UNIQUE KEY `idx_BizVersionId` (`BizVersionId`),
                                                   KEY `idx_ObjectRelId` (`ObjectRelId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


ALTER TABLE `tb_test_data_matrix_info_sl_nb`
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `ModifiedDate`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_matrix_group_id` int(11) NULL COMMENT '测试单位分组标识' AFTER `report_no`,
    ADD COLUMN `test_line_instance_id` varchar(64) NULL COMMENT '测试项实例标识' AFTER `test_matrix_group_id`,
    ADD COLUMN `evaluation_name` varchar(255) NULL COMMENT '测试项名称' AFTER `test_line_instance_id`,
    ADD COLUMN `test_line_status` varchar(255) NULL COMMENT '测试项状态' AFTER `evaluation_name`,
    ADD COLUMN `test_line_remark` varchar(255) NULL COMMENT '测试项备注' AFTER `test_line_status`,
    ADD COLUMN `citation_full_name` varchar(255) NULL COMMENT '测试标准拼接名称' AFTER `test_line_remark`,
    ADD COLUMN `sample_instance_id` varchar(36) NULL COMMENT '样品实例标识' AFTER `citation_full_name`,
    ADD COLUMN `sample_group` text NULL COMMENT '样品分组信息' AFTER `sample_instance_id`,
    ADD COLUMN `sample_parent_id` varchar(36) NULL COMMENT '样品父级标识' AFTER `sample_group`,
    ADD COLUMN `sample_type` varchar(255) NULL COMMENT '样品类型' AFTER `sample_parent_id`,
    ADD COLUMN `category` varchar(255) NULL COMMENT '样品分类' AFTER `sample_type`,
    ADD COLUMN `material_color` varchar(255) NULL COMMENT '物料颜色' AFTER `category`,
    ADD COLUMN `composition` varchar(255) NULL COMMENT '物料材质' AFTER `material_color`,
    ADD COLUMN `material_description` varchar(255) NULL COMMENT '物料描述' AFTER `composition`,
    ADD COLUMN `material_end_use` varchar(255) NULL COMMENT '物料用途' AFTER `material_description`,
    ADD COLUMN `applicable_flag` varchar(255) NULL COMMENT 'NC样品标识' AFTER `material_end_use`,
    ADD COLUMN `material_other_sample_info` varchar(255) NULL COMMENT '其他样品信息' AFTER `applicable_flag`,
    ADD COLUMN `material_remark` varchar(255) NULL COMMENT '样品备注信息' AFTER `material_other_sample_info`,
    ADD COLUMN `conclusion_code` varchar(255) NULL COMMENT '测试结论编码' AFTER `material_remark`,
    ADD COLUMN `customer_conclusion` varchar(50) NULL COMMENT '客户测试结论' AFTER `conclusion_code`,
    ADD COLUMN `conclusion_remark` varchar(255) NULL COMMENT '测试结论备注' AFTER `customer_conclusion`,
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `last_modified_timestamp` datetime(0) NULL ON
UPDATE CURRENT_TIMESTAMP(0) COMMENT ' DB 自动更新，不允许程序设置 ' AFTER `rd_report_id`;


ALTER TABLE `tb_test_data_matrix_info_sl_nj`
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `ModifiedDate`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_matrix_group_id` int(11) NULL COMMENT '测试单位分组标识' AFTER `report_no`,
    ADD COLUMN `test_line_instance_id` varchar(64) NULL COMMENT '测试项实例标识' AFTER `test_matrix_group_id`,
    ADD COLUMN `evaluation_name` varchar(255) NULL COMMENT '测试项名称' AFTER `test_line_instance_id`,
    ADD COLUMN `test_line_status` varchar(255) NULL COMMENT '测试项状态' AFTER `evaluation_name`,
    ADD COLUMN `test_line_remark` varchar(255) NULL COMMENT '测试项备注' AFTER `test_line_status`,
    ADD COLUMN `citation_full_name` varchar(255) NULL COMMENT '测试标准拼接名称' AFTER `test_line_remark`,
    ADD COLUMN `sample_instance_id` varchar(36) NULL COMMENT '样品实例标识' AFTER `citation_full_name`,
    ADD COLUMN `sample_group` text NULL COMMENT '样品分组信息' AFTER `sample_instance_id`,
    ADD COLUMN `sample_parent_id` varchar(36) NULL COMMENT '样品父级标识' AFTER `sample_group`,
    ADD COLUMN `sample_type` varchar(255) NULL COMMENT '样品类型' AFTER `sample_parent_id`,
    ADD COLUMN `category` varchar(255) NULL COMMENT '样品分类' AFTER `sample_type`,
    ADD COLUMN `material_color` varchar(255) NULL COMMENT '物料颜色' AFTER `category`,
    ADD COLUMN `composition` varchar(255) NULL COMMENT '物料材质' AFTER `material_color`,
    ADD COLUMN `material_description` varchar(255) NULL COMMENT '物料描述' AFTER `composition`,
    ADD COLUMN `material_end_use` varchar(255) NULL COMMENT '物料用途' AFTER `material_description`,
    ADD COLUMN `applicable_flag` varchar(255) NULL COMMENT 'NC样品标识' AFTER `material_end_use`,
    ADD COLUMN `material_other_sample_info` varchar(255) NULL COMMENT '其他样品信息' AFTER `applicable_flag`,
    ADD COLUMN `material_remark` varchar(255) NULL COMMENT '样品备注信息' AFTER `material_other_sample_info`,
    ADD COLUMN `conclusion_code` varchar(255) NULL COMMENT '测试结论编码' AFTER `material_remark`,
    ADD COLUMN `customer_conclusion` varchar(50) NULL COMMENT '客户测试结论' AFTER `conclusion_code`,
    ADD COLUMN `conclusion_remark` varchar(255) NULL COMMENT '测试结论备注' AFTER `customer_conclusion`,
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `last_modified_timestamp` datetime(0) NULL ON
UPDATE CURRENT_TIMESTAMP(0) COMMENT ' DB 自动更新，不允许程序设置 ' AFTER `rd_report_id`;



ALTER TABLE `tb_test_data_matrix_info_sl_qd`
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `ModifiedDate`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_matrix_group_id` int(11) NULL COMMENT '测试单位分组标识' AFTER `report_no`,
    ADD COLUMN `test_line_instance_id` varchar(64) NULL COMMENT '测试项实例标识' AFTER `test_matrix_group_id`,
    ADD COLUMN `evaluation_name` varchar(255) NULL COMMENT '测试项名称' AFTER `test_line_instance_id`,
    ADD COLUMN `test_line_status` varchar(255) NULL COMMENT '测试项状态' AFTER `evaluation_name`,
    ADD COLUMN `test_line_remark` varchar(255) NULL COMMENT '测试项备注' AFTER `test_line_status`,
    ADD COLUMN `citation_full_name` varchar(255) NULL COMMENT '测试标准拼接名称' AFTER `test_line_remark`,
    ADD COLUMN `sample_instance_id` varchar(36) NULL COMMENT '样品实例标识' AFTER `citation_full_name`,
    ADD COLUMN `sample_group` text NULL COMMENT '样品分组信息' AFTER `sample_instance_id`,
    ADD COLUMN `sample_parent_id` varchar(36) NULL COMMENT '样品父级标识' AFTER `sample_group`,
    ADD COLUMN `sample_type` varchar(255) NULL COMMENT '样品类型' AFTER `sample_parent_id`,
    ADD COLUMN `category` varchar(255) NULL COMMENT '样品分类' AFTER `sample_type`,
    ADD COLUMN `material_color` varchar(255) NULL COMMENT '物料颜色' AFTER `category`,
    ADD COLUMN `composition` varchar(255) NULL COMMENT '物料材质' AFTER `material_color`,
    ADD COLUMN `material_description` varchar(255) NULL COMMENT '物料描述' AFTER `composition`,
    ADD COLUMN `material_end_use` varchar(255) NULL COMMENT '物料用途' AFTER `material_description`,
    ADD COLUMN `applicable_flag` varchar(255) NULL COMMENT 'NC样品标识' AFTER `material_end_use`,
    ADD COLUMN `material_other_sample_info` varchar(255) NULL COMMENT '其他样品信息' AFTER `applicable_flag`,
    ADD COLUMN `material_remark` varchar(255) NULL COMMENT '样品备注信息' AFTER `material_other_sample_info`,
    ADD COLUMN `conclusion_code` varchar(255) NULL COMMENT '测试结论编码' AFTER `material_remark`,
    ADD COLUMN `customer_conclusion` varchar(50) NULL COMMENT '客户测试结论' AFTER `conclusion_code`,
    ADD COLUMN `conclusion_remark` varchar(255) NULL COMMENT '测试结论备注' AFTER `customer_conclusion`,
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `last_modified_timestamp` datetime(0) NULL ON
UPDATE CURRENT_TIMESTAMP(0) COMMENT ' DB 自动更新，不允许程序设置 ' AFTER `rd_report_id`;




ALTER TABLE `tb_test_data_matrix_info_sl_sh`
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `ModifiedDate`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_matrix_group_id` int(11) NULL COMMENT '测试单位分组标识' AFTER `report_no`,
    ADD COLUMN `test_line_instance_id` varchar(64) NULL COMMENT '测试项实例标识' AFTER `test_matrix_group_id`,
    ADD COLUMN `evaluation_name` varchar(255) NULL COMMENT '测试项名称' AFTER `test_line_instance_id`,
    ADD COLUMN `test_line_status` varchar(255) NULL COMMENT '测试项状态' AFTER `evaluation_name`,
    ADD COLUMN `test_line_remark` varchar(255) NULL COMMENT '测试项备注' AFTER `test_line_status`,
    ADD COLUMN `citation_full_name` varchar(255) NULL COMMENT '测试标准拼接名称' AFTER `test_line_remark`,
    ADD COLUMN `sample_instance_id` varchar(36) NULL COMMENT '样品实例标识' AFTER `citation_full_name`,
    ADD COLUMN `sample_group` text NULL COMMENT '样品分组信息' AFTER `sample_instance_id`,
    ADD COLUMN `sample_parent_id` varchar(36) NULL COMMENT '样品父级标识' AFTER `sample_group`,
    ADD COLUMN `sample_type` varchar(255) NULL COMMENT '样品类型' AFTER `sample_parent_id`,
    ADD COLUMN `category` varchar(255) NULL COMMENT '样品分类' AFTER `sample_type`,
    ADD COLUMN `material_color` varchar(255) NULL COMMENT '物料颜色' AFTER `category`,
    ADD COLUMN `composition` varchar(255) NULL COMMENT '物料材质' AFTER `material_color`,
    ADD COLUMN `material_description` varchar(255) NULL COMMENT '物料描述' AFTER `composition`,
    ADD COLUMN `material_end_use` varchar(255) NULL COMMENT '物料用途' AFTER `material_description`,
    ADD COLUMN `applicable_flag` varchar(255) NULL COMMENT 'NC样品标识' AFTER `material_end_use`,
    ADD COLUMN `material_other_sample_info` varchar(255) NULL COMMENT '其他样品信息' AFTER `applicable_flag`,
    ADD COLUMN `material_remark` varchar(255) NULL COMMENT '样品备注信息' AFTER `material_other_sample_info`,
    ADD COLUMN `conclusion_code` varchar(255) NULL COMMENT '测试结论编码' AFTER `material_remark`,
    ADD COLUMN `customer_conclusion` varchar(50) NULL COMMENT '客户测试结论' AFTER `conclusion_code`,
    ADD COLUMN `conclusion_remark` varchar(255) NULL COMMENT '测试结论备注' AFTER `customer_conclusion`,
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `last_modified_timestamp` datetime(0) NULL ON
UPDATE CURRENT_TIMESTAMP(0) COMMENT ' DB 自动更新，不允许程序设置 ' AFTER `rd_report_id`;



ALTER TABLE `tb_test_data_matrix_info_sl_tj`
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `ModifiedDate`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_matrix_group_id` int(11) NULL COMMENT '测试单位分组标识' AFTER `report_no`,
    ADD COLUMN `test_line_instance_id` varchar(64) NULL COMMENT '测试项实例标识' AFTER `test_matrix_group_id`,
    ADD COLUMN `evaluation_name` varchar(255) NULL COMMENT '测试项名称' AFTER `test_line_instance_id`,
    ADD COLUMN `test_line_status` varchar(255) NULL COMMENT '测试项状态' AFTER `evaluation_name`,
    ADD COLUMN `test_line_remark` varchar(255) NULL COMMENT '测试项备注' AFTER `test_line_status`,
    ADD COLUMN `citation_full_name` varchar(255) NULL COMMENT '测试标准拼接名称' AFTER `test_line_remark`,
    ADD COLUMN `sample_instance_id` varchar(36) NULL COMMENT '样品实例标识' AFTER `citation_full_name`,
    ADD COLUMN `sample_group` text NULL COMMENT '样品分组信息' AFTER `sample_instance_id`,
    ADD COLUMN `sample_parent_id` varchar(36) NULL COMMENT '样品父级标识' AFTER `sample_group`,
    ADD COLUMN `sample_type` varchar(255) NULL COMMENT '样品类型' AFTER `sample_parent_id`,
    ADD COLUMN `category` varchar(255) NULL COMMENT '样品分类' AFTER `sample_type`,
    ADD COLUMN `material_color` varchar(255) NULL COMMENT '物料颜色' AFTER `category`,
    ADD COLUMN `composition` varchar(255) NULL COMMENT '物料材质' AFTER `material_color`,
    ADD COLUMN `material_description` varchar(255) NULL COMMENT '物料描述' AFTER `composition`,
    ADD COLUMN `material_end_use` varchar(255) NULL COMMENT '物料用途' AFTER `material_description`,
    ADD COLUMN `applicable_flag` varchar(255) NULL COMMENT 'NC样品标识' AFTER `material_end_use`,
    ADD COLUMN `material_other_sample_info` varchar(255) NULL COMMENT '其他样品信息' AFTER `applicable_flag`,
    ADD COLUMN `material_remark` varchar(255) NULL COMMENT '样品备注信息' AFTER `material_other_sample_info`,
    ADD COLUMN `conclusion_code` varchar(255) NULL COMMENT '测试结论编码' AFTER `material_remark`,
    ADD COLUMN `customer_conclusion` varchar(50) NULL COMMENT '客户测试结论' AFTER `conclusion_code`,
    ADD COLUMN `conclusion_remark` varchar(255) NULL COMMENT '测试结论备注' AFTER `customer_conclusion`,
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `last_modified_timestamp` datetime(0) NULL ON
UPDATE CURRENT_TIMESTAMP(0) COMMENT ' DB 自动更新，不允许程序设置 ' AFTER `rd_report_id`;



ALTER TABLE `tb_test_data_matrix_info_sl_xm`
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT 'Trims系统实验室标识' AFTER `ModifiedDate`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT '订单号' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT '报告号' AFTER `order_no`,
    ADD COLUMN `test_matrix_group_id` int(11) NULL COMMENT '测试单位分组标识' AFTER `report_no`,
    ADD COLUMN `test_line_instance_id` varchar(64) NULL COMMENT '测试项实例标识' AFTER `test_matrix_group_id`,
    ADD COLUMN `evaluation_name` varchar(255) NULL COMMENT '测试项名称' AFTER `test_line_instance_id`,
    ADD COLUMN `test_line_status` varchar(255) NULL COMMENT '测试项状态' AFTER `evaluation_name`,
    ADD COLUMN `test_line_remark` varchar(255) NULL COMMENT '测试项备注' AFTER `test_line_status`,
    ADD COLUMN `citation_full_name` varchar(255) NULL COMMENT '测试标准拼接名称' AFTER `test_line_remark`,
    ADD COLUMN `sample_instance_id` varchar(36) NULL COMMENT '样品实例标识' AFTER `citation_full_name`,
    ADD COLUMN `sample_group` text NULL COMMENT '样品分组信息' AFTER `sample_instance_id`,
    ADD COLUMN `sample_parent_id` varchar(36) NULL COMMENT '样品父级标识' AFTER `sample_group`,
    ADD COLUMN `sample_type` varchar(255) NULL COMMENT '样品类型' AFTER `sample_parent_id`,
    ADD COLUMN `category` varchar(255) NULL COMMENT '样品分类' AFTER `sample_type`,
    ADD COLUMN `material_color` varchar(255) NULL COMMENT '物料颜色' AFTER `category`,
    ADD COLUMN `composition` varchar(255) NULL COMMENT '物料材质' AFTER `material_color`,
    ADD COLUMN `material_description` varchar(255) NULL COMMENT '物料描述' AFTER `composition`,
    ADD COLUMN `material_end_use` varchar(255) NULL COMMENT '物料用途' AFTER `material_description`,
    ADD COLUMN `applicable_flag` varchar(255) NULL COMMENT 'NC样品标识' AFTER `material_end_use`,
    ADD COLUMN `material_other_sample_info` varchar(255) NULL COMMENT '其他样品信息' AFTER `applicable_flag`,
    ADD COLUMN `material_remark` varchar(255) NULL COMMENT '样品备注信息' AFTER `material_other_sample_info`,
    ADD COLUMN `conclusion_code` varchar(255) NULL COMMENT '测试结论编码' AFTER `material_remark`,
    ADD COLUMN `customer_conclusion` varchar(50) NULL COMMENT '客户测试结论' AFTER `conclusion_code`,
    ADD COLUMN `conclusion_remark` varchar(255) NULL COMMENT '测试结论备注' AFTER `customer_conclusion`,
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `last_modified_timestamp` datetime(0) NULL ON
UPDATE CURRENT_TIMESTAMP(0) COMMENT ' DB 自动更新，不允许程序设置 ' AFTER `rd_report_id`;


CREATE TABLE `tb_test_sample` (
                                  `id` bigint(20) NOT NULL COMMENT 'ID,Primary key',
                                  `sample_instance_id` varchar(64) DEFAULT NULL,
                                  `sample_parent_id` varchar(36) DEFAULT NULL,
                                  `rd_report_matrix_id` bigint(20) DEFAULT NULL,
                                  `sample_no` varchar(60) DEFAULT NULL COMMENT '样品编号',
                                  `sample_type` int(11) DEFAULT NULL COMMENT '样品类型',
                                  `sample_seq` int(11) DEFAULT NULL COMMENT '样品顺序',
                                  `order_no` varchar(50) DEFAULT NULL COMMENT '订单编号',
                                  `category` varchar(10) DEFAULT NULL COMMENT '物料分类',
                                  `description` varchar(2048) DEFAULT NULL COMMENT '物料描述',
                                  `composition` varchar(500) DEFAULT NULL COMMENT '物料材质',
                                  `color` varchar(500) DEFAULT NULL COMMENT '物料颜色',
                                  `sample_remark` varchar(4000) DEFAULT NULL COMMENT '样品备注信息',
                                  `end_use` varchar(500) DEFAULT NULL COMMENT '物料用途',
                                  `material` varchar(300) DEFAULT NULL COMMENT '物料名称',
                                  `other_sample_info` text COMMENT '其它样品信息',
                                  `applicable_flag` tinyint(1) DEFAULT NULL COMMENT 'NC 样品标识',
                                  `active_indicator` tinyint(1) NOT NULL DEFAULT '1' COMMENT '0: inactive, 1: active',
                                  `created_date` datetime DEFAULT NULL COMMENT 'CreatedDate',
                                  `created_by` varchar(50) DEFAULT NULL COMMENT 'CreatedBy',
                                  `modified_date` datetime DEFAULT NULL COMMENT 'ModifiedDate',
                                  `modified_by` varchar(50) DEFAULT NULL COMMENT 'ModifiedBy',
                                  `last_modified_timestamp` timestamp(3) NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
                                  PRIMARY KEY (`id`),
                                  KEY `idx_orderNo_sampleNo` (`order_no`,`sample_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


CREATE TABLE `tb_test_sample_group` (
                                        `id` bigint(20) NOT NULL COMMENT 'ID,Primary key',
                                        `sample_group_id` varchar(64) DEFAULT NULL,
                                        `sample_id` varchar(64) DEFAULT NULL COMMENT 'FK tb_TestSample',
                                        `main_sample_flag` int(11) DEFAULT NULL COMMENT '主测试样标识',
                                        `active_indicator` tinyint(1) NOT NULL DEFAULT '1' COMMENT '0: inactive, 1: active',
                                        `created_date` datetime DEFAULT NULL COMMENT 'CreatedDate',
                                        `created_by` varchar(50) DEFAULT NULL COMMENT 'CreatedBy',
                                        `modified_date` datetime DEFAULT NULL COMMENT 'ModifiedDate',
                                        `modified_by` varchar(50) DEFAULT NULL COMMENT 'ModifiedBy',
                                        `last_modified_timestamp` timestamp(3) NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
                                        PRIMARY KEY (`id`),
                                        KEY `FK_Reference_12` (`sample_group_id`),
                                        KEY `FK_Reference_13` (`sample_id`),
                                        KEY `index_LastModifiedTimestamp` (`last_modified_timestamp`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

ALTER TABLE  `tb_report`
    ADD COLUMN `system_id` bigint(20) NULL AFTER `bu_id`,
ADD COLUMN `report_due_date` datetime NULL AFTER `report_status`;


ALTER TABLE `tb_report_ext`
    MODIFY COLUMN `request_json` mediumtext NULL AFTER `rd_report_id`;

CREATE TABLE `tb_report_invoice` (
                                     `id` bigint(20) NOT NULL,
                                     `lab_id` bigint(20) DEFAULT NULL COMMENT 'Trims系统实验室标识',
                                     `rd_report_id` bigint(20) DEFAULT NULL,
                                     `system_id` bigint(20) DEFAULT NULL,
                                     `order_no` varchar(50) DEFAULT NULL COMMENT '订单号',
                                     `report_no` varchar(50) DEFAULT NULL COMMENT '报告号',
                                     `boss_order_no` varchar(100) DEFAULT NULL COMMENT 'Boss订单号',
                                     `product_code` varchar(500) DEFAULT NULL COMMENT '产品编码',
                                     `cost_center` varchar(500) DEFAULT NULL COMMENT '费用中心',
                                     `project_template` varchar(500) DEFAULT NULL COMMENT '项目模板',
                                     `invoice_date` datetime DEFAULT NULL COMMENT '发票开具时间',
                                     `invoice_no` varchar(50) DEFAULT NULL COMMENT 'Boss 发票号',
                                     `currency` varchar(100) DEFAULT NULL COMMENT '币种',
                                     `total_amount` decimal(20,6) DEFAULT NULL COMMENT '总金额',
                                     `invoice_status` int(10) unsigned DEFAULT NULL COMMENT '发票状态',
                                     `active_indicator` tinyint(1) DEFAULT '1' COMMENT '0: inactive, 1: active',
                                     `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
                                     `created_date` datetime DEFAULT NULL COMMENT '创建时间',
                                     `modified_by` varchar(50) DEFAULT NULL COMMENT '修改人',
                                     `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
                                     `last_modified_date` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT 'DB 自动更新，不允许程序设置',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


CREATE TABLE `tb_report_matrix_lang` (
                                         `id` bigint(20) NOT NULL COMMENT 'RD 数据唯一标识',
                                         `rd_report_matrix_id` bigint(20) DEFAULT NULL COMMENT 'RD Report matrix标识',
                                         `language_id` int(11) DEFAULT NULL COMMENT '语言标识',
                                         `evaluation_alias` varchar(500) DEFAULT NULL COMMENT '测试项别名',
                                         `evaluation_name` varchar(255) DEFAULT NULL COMMENT '测试项名称',
                                         `citation_name` varchar(1000) DEFAULT NULL COMMENT '测试标准名称',
                                         `citation_full_name` varchar(255) DEFAULT NULL COMMENT '测试标准全称',
                                         `method_desc` varchar(512) DEFAULT NULL,
                                         `customer_conclusion` varchar(50) DEFAULT NULL COMMENT '客户测试结论',
                                         `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
                                         `created_date` datetime DEFAULT NULL COMMENT '创建时间',
                                         `modified_by` varchar(50) DEFAULT NULL COMMENT '修改人',
                                         `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
                                         PRIMARY KEY (`id`),
                                         KEY `idx_rd_report_matrix_id` (`rd_report_matrix_id`),
                                         KEY `idx_createdDate` (`created_date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

ALTER TABLE `tb_report_product_dff`
    ADD COLUMN `rd_report_id` bigint(20) NULL COMMENT 'report唯一标识' AFTER `lab_id`;

CREATE TABLE `tb_report_test_result_lang` (
                                              `id` bigint(20) NOT NULL COMMENT 'RD 数据唯一标识',
                                              `rd_report_test_result_id` bigint(20) DEFAULT NULL COMMENT 'RD Report Test Result标识',
                                              `language_id` int(11) DEFAULT NULL COMMENT '语言标识',
                                              `test_result_full_name` varchar(500) DEFAULT NULL COMMENT '测试项目名称，拼接字段',
                                              `result_value_remark` varchar(500) DEFAULT NULL COMMENT '测试结果备注',
                                              `result_unit` varchar(1024) DEFAULT NULL COMMENT '测试结果单位',
                                              `limit_value_full_name` varchar(500) DEFAULT NULL COMMENT '参考标准，拼接字段',
                                              `limit_unit` varchar(4000) DEFAULT NULL COMMENT '参考标准单位',
                                              `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
                                              `created_date` datetime NOT NULL COMMENT '创建时间',
                                              `modified_by` varchar(50) DEFAULT NULL COMMENT '修改人',
                                              `modified_date` datetime NOT NULL COMMENT '修改时间',
                                              PRIMARY KEY (`id`),
                                              KEY `idx_rd_report_test_result_id` (`rd_report_test_result_id`),
                                              KEY `idx_createdDate` (`created_date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


ALTER TABLE tb_test_data_object_rel ADD COLUMN `LabId` bigint(20) DEFAULT NULL COMMENT 'Lab Id';


CREATE TABLE `tb_report_pp_tl_rel` (
                                       `id` bigint(20) NOT NULL COMMENT 'RD 数据唯一标识',
                                       `rd_report_id` bigint(20) DEFAULT NULL COMMENT 'RD 报告唯一标识',
                                       `lab_id` bigint(20) DEFAULT NULL COMMENT 'Trims系统实验室标识',
                                       `order_no` varchar(50) DEFAULT NULL COMMENT '订单号',
                                       `report_no` varchar(50) DEFAULT NULL COMMENT '报告号',
                                       `test_matrix_group_id` int(11) DEFAULT NULL COMMENT '测试单位分组标识',
                                       `test_line_instance_id` int(11) DEFAULT NULL COMMENT '测试项实例标识',
                                       `pp_no` int(11) DEFAULT NULL COMMENT '测试包编码',
                                       `pp_name` varchar(255) DEFAULT NULL COMMENT '测试包名称',
                                       `aid` bigint(20) DEFAULT NULL COMMENT '测试包下测试项标识',
                                       `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
                                       `created_date` datetime NOT NULL COMMENT '创建时间',
                                       `modified_by` varchar(50) DEFAULT NULL COMMENT '修改人',
                                       `modified_date` datetime NOT NULL COMMENT '修改时间',
                                       `last_modified_timestamp` timestamp(3) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(3) COMMENT 'DB 自动更新，不允许程序设置',
                                       PRIMARY KEY (`id`),
                                       KEY `idx_labId_reportNo` (`lab_id`,`report_no`) USING BTREE,
                                       KEY `idx_createdDate` (`created_date`) USING BTREE,
                                       KEY `idx_rd_report_id` (`rd_report_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


ALTER TABLE `tb_attachment`
    ADD COLUMN `system_id` bigint(20) NULL COMMENT '系统id' AFTER `order_no`;

ALTER TABLE `tb_report_product_dff`
    MODIFY COLUMN `label_code` varchar(100) CHARACTER SET utf8mb4 NULL DEFAULT NULL COMMENT 'BU 标签编码' AFTER `language_id`,
    MODIFY COLUMN `label_name` varchar(100) CHARACTER SET utf8mb4 NULL DEFAULT NULL COMMENT 'BU 标签名称' AFTER `label_code`,
    MODIFY COLUMN `field_code` varchar(50) CHARACTER SET utf8mb4 NULL DEFAULT NULL COMMENT 'DFF 字段编码' AFTER `label_name`,
    MODIFY COLUMN `customer_label` varchar(50) CHARACTER SET utf8mb4 NULL DEFAULT NULL COMMENT '客户标签名称' AFTER `field_code`,
    MODIFY COLUMN `data_type` varchar(50) CHARACTER SET utf8mb4 NULL DEFAULT NULL COMMENT '数据类型' AFTER `customer_label`,
    MODIFY COLUMN `value` varchar(500) CHARACTER SET utf8mb4 NOT NULL COMMENT '数值' AFTER `data_type`;

CREATE TABLE `tb_quotation` (
                                `id` bigint(20) NOT NULL COMMENT 'RD 唯一标识',
                                `lab_id` bigint(20) DEFAULT NULL COMMENT 'Trims系统实验室标识',
                                `system_id` bigint(20) DEFAULT NULL,
                                `order_no` varchar(50) DEFAULT NULL COMMENT '订单号',
                                `report_no` varchar(50) DEFAULT NULL COMMENT '报告号',
                                `quotation_no` varchar(50) DEFAULT NULL COMMENT '报价单号',
                                `currency_code` varchar(50) DEFAULT NULL COMMENT '币种编码',
                                `payer_customer_name` varchar(300) DEFAULT NULL COMMENT '付款方公司名称',
                                `payer_boss_number` bigint(20) DEFAULT NULL COMMENT '付款方Boss编码',
                                `service_item_type` varchar(50) DEFAULT NULL COMMENT '服务类型',
                                `service_item_name` varchar(2000) DEFAULT NULL COMMENT '服务名称',
                                `service_item_seq` int(11) DEFAULT NULL COMMENT '顺序',
                                `special_offer_flag` int(11) DEFAULT NULL COMMENT '是否为特价',
                                `quantity` int(11) DEFAULT NULL COMMENT '数量',
                                `service_item_list_unit_price` decimal(10,2) DEFAULT NULL COMMENT 'Trims 单价',
                                `service_item_sales_unit_price` decimal(10,2) DEFAULT NULL COMMENT '销售单价',
                                `service_item_discount` decimal(18,5) DEFAULT NULL COMMENT '行折扣',
                                `service_item_exchange_rate_price` decimal(38,19) DEFAULT NULL COMMENT '汇率',
                                `service_item_sur_charge_price` decimal(18,5) DEFAULT NULL COMMENT 'charge费用',
                                `service_item_net_amount` decimal(18,5) DEFAULT NULL COMMENT '行上税前金额',
                                `service_item_vat_amount` decimal(18,5) DEFAULT NULL COMMENT '行上税额',
                                `service_item_total_amount` decimal(18,5) DEFAULT NULL COMMENT '行上总金额',
                                `sum_net_amount` decimal(10,2) DEFAULT NULL COMMENT '汇总金额',
                                `sum_vat_amount` decimal(10,2) DEFAULT NULL COMMENT '税费',
                                `total_amount` decimal(10,2) DEFAULT NULL COMMENT '汇总金额',
                                `adjustment_amount` decimal(10,2) DEFAULT NULL COMMENT '调整金额',
                                `adjustment_discount` decimal(10,2) DEFAULT NULL COMMENT '调整折扣',
                                `final_amount` decimal(10,2) DEFAULT NULL COMMENT '最终金额',
                                `pp_no` int(11) DEFAULT NULL COMMENT '关联PP标识',
                                `test_line_id` bigint(20) DEFAULT NULL COMMENT '关联TL标识',
                                `citation_type` tinyint(4) DEFAULT NULL COMMENT '测试标准类型',
                                `citation_id` int(11) DEFAULT NULL COMMENT '测试标准标识',
                                `citation_name` varchar(255) DEFAULT NULL COMMENT '测试标准名称',
                                `citation_full_name` varchar(255) DEFAULT NULL COMMENT '测试标准拼接名称',
                                `quotation_version_id` varchar(100) DEFAULT NULL COMMENT '报价单版本',
                                `quotation_status` tinyint(4) DEFAULT NULL COMMENT '报价单状态',
                                `active_indicator` tinyint(4) DEFAULT NULL COMMENT '是否有效',
                                `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
                                `created_date` datetime DEFAULT NULL COMMENT '创建时间',
                                `modified_by` varchar(50) DEFAULT NULL COMMENT '修改人',
                                `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
                                `last_modified_timestamp` timestamp(3) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(3) COMMENT 'DB 自动更新，不允许程序设置',
                                PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


CREATE TABLE `tb_quotation_lang` (
                                     `id` bigint(20) NOT NULL COMMENT 'RD 唯一标识',
                                     `rt_quotation_id` bigint(20) NOT NULL COMMENT 'RD 报价单标识',
                                     `language_id` int(11) DEFAULT NULL COMMENT '语言标识',
                                     `payer_customer_name` varchar(300) DEFAULT NULL COMMENT '付款方公司名称',
                                     `citation_name` varchar(255) DEFAULT NULL COMMENT '测试标准名称',
                                     `citation_full_name` varchar(255) DEFAULT NULL COMMENT '测试标准名称拼接字段',
                                     `service_item_name` varchar(2000) DEFAULT NULL COMMENT '服务项目名称',
                                     `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
                                     `created_date` datetime DEFAULT NULL COMMENT '创建时间',
                                     `modified_by` varchar(50) DEFAULT NULL COMMENT '修改人',
                                     `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
                                     `last_modified_timestamp` timestamp(3) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(3) COMMENT 'DB 自动更新，不允许程序设置',
                                     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


CREATE TABLE `tb_quotation_invoice_rel` (
                                            `id` bigint(20) NOT NULL COMMENT 'RD 附件标识',
                                            `quotation_no` varchar(50) DEFAULT NULL COMMENT 'RD 报价单标识',
                                            `invoice_no` varchar(50) DEFAULT NULL COMMENT 'RD 发票标识',
                                            `system_id` bigint(20) DEFAULT NULL,
                                            `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
                                            `created_date` datetime DEFAULT NULL COMMENT '创建时间',
                                            `modified_by` varchar(50) DEFAULT NULL COMMENT '修改人',
                                            `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
                                            `last_modified_timestamp` timestamp(3) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(3) COMMENT 'DB 自动更新，不允许程序设置',
                                            PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
