package com.sgs.testdatabiz.domain.service.testdata.factory;

import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.core.util.DateUtils;
import com.sgs.testdatabiz.dbstorages.mybatis.enums.ActiveIndicatorEnum;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataObjectRelPO;
import com.sgs.testdatabiz.domain.service.testdata.model.BizVersionId;
import com.sgs.testdatabiz.facade.model.testdata.ReportTestDataInfo;
import org.springframework.stereotype.Component;

import java.util.UUID;

/**
 * 测试数据对象关系工厂类
 * 负责创建和构造TestDataObjectRelPO对象
 * 
 * <AUTHOR>
 */
@Component
public class TestDataObjectRelFactory {
    
    /**
     * 从ReportTestDataInfo创建TestDataObjectRelPO
     * 
     * @param reqObject 报告测试数据信息
     * @return 构造好的TestDataObjectRelPO对象
     * @throws IllegalArgumentException 如果reqObject为null
     */
    public TestDataObjectRelPO createObjectRelation(ReportTestDataInfo reqObject) {
        if (reqObject == null) {
            throw new IllegalArgumentException("ReportTestDataInfo cannot be null");
        }
        
        TestDataObjectRelPO objectRel = new TestDataObjectRelPO();
        
        // 设置基本标识信息
        objectRel.setId(UUID.randomUUID().toString());
        objectRel.setProductLineCode(reqObject.getProductLineCode());
        objectRel.setLabCode(reqObject.getLabCode());
        
        // 设置订单相关信息
        objectRel.setOrderNo(reqObject.getOrderNo());
        objectRel.setParentOrderNo(reqObject.getParentOrderNo());
        objectRel.setReportNo(reqObject.getReportNo());
        objectRel.setObjectNo(reqObject.getSubContractNo());
        
        // 设置外部系统相关信息
        objectRel.setExternalId(reqObject.getExternalId());
        objectRel.setExternalNo(reqObject.getExternalNo());
        objectRel.setExternalObjectNo(reqObject.getExternalObjectNo());
        
        // 设置其他业务信息
        objectRel.setLanguageId(reqObject.getLanguageId());
        objectRel.setCompleteDate(reqObject.getCompletedDate());
        objectRel.setSourceType(reqObject.getSourceType()); // 1、slim 2、job 3、starlims 4、fast 5、subcontract 6、new
        
        // 设置状态和版本信息
        objectRel.setActiveIndicator(ActiveIndicatorEnum.ACTIVE.getValue()); // 0: inactive, 1: active
        objectRel.setBizVersionId(BizVersionId.fromObjectRel(objectRel).getValue());
        
        // 设置审计信息
        objectRel.setCreatedBy(reqObject.getRegionAccount());
        objectRel.setCreatedDate(DateUtils.getNow());
        objectRel.setModifiedBy(reqObject.getRegionAccount());
        objectRel.setModifiedDate(DateUtils.getNow());
        
        // 设置客户接口排除标识
        objectRel.setExcludeCustomerInterface(
            Func.isEmpty(reqObject.getExcludeCustomerInterface()) ? "0" : reqObject.getExcludeCustomerInterface()
        );
        
        return objectRel;
    }
    
    /**
     * 创建原始对象关系（用于处理originalReportNo场景）
     * 
     * @param reqObject 报告测试数据信息
     * @return 构造好的原始TestDataObjectRelPO对象，如果不需要则返回null
     */
    public TestDataObjectRelPO createOriginalObjectRelation(ReportTestDataInfo reqObject) {
        if (reqObject == null || Func.isBlank(reqObject.getOriginalReportNo())) {
            return null;
        }
        
        TestDataObjectRelPO rel = new TestDataObjectRelPO();
        rel.setReportNo(reqObject.getReportNo());
        rel.setObjectNo(reqObject.getSubContractNo());
        rel.setExternalNo(reqObject.getExternalNo());
        rel.setSourceType(reqObject.getSourceType());
        // 原报告编号 目前只限于StarLims：取originalReportNo
        rel.setExternalObjectNo(reqObject.getOriginalReportNo());
        
        return rel;
    }
    
    /**
     * 为现有对象设置无效状态（用于原始对象关系的处理）
     * 
     * @param objectRel 需要设置为无效的对象关系
     * @return 设置后的对象关系
     * @throws IllegalArgumentException 如果objectRel为null
     */
    public TestDataObjectRelPO markAsInactive(TestDataObjectRelPO objectRel) {
        if (objectRel == null) {
            throw new IllegalArgumentException("TestDataObjectRelPO cannot be null");
        }
        
        objectRel.setActiveIndicator(ActiveIndicatorEnum.INACTIVE.getValue());
        return objectRel;
    }
    
    /**
     * 更新对象关系的业务版本ID
     * 
     * @param objectRel 需要更新的对象关系
     * @return 更新后的对象关系
     * @throws IllegalArgumentException 如果objectRel为null
     */
    public TestDataObjectRelPO updateBizVersionId(TestDataObjectRelPO objectRel) {
        if (objectRel == null) {
            throw new IllegalArgumentException("TestDataObjectRelPO cannot be null");
        }
        
        objectRel.setBizVersionId(BizVersionId.fromObjectRel(objectRel).getValue());
        return objectRel;
    }
}