package com.sgs.testdatabiz.domain.service.testdata.result;

/**
 * 测试数据保存结果
 * 封装带有成功/失败状态的操作结果
 * 
 * <AUTHOR>
 * @create 2024-01-01
 */
public class TestDataSaveResult {
    
    /**
     * 操作是否成功
     */
    private final boolean success;
    
    /**
     * 错误消息（当操作失败时）
     */
    private final String errorMessage;
    
    /**
     * 错误代码（当操作失败时）
     */
    private final String errorCode;
    
    /**
     * 私有构造函数
     */
    private TestDataSaveResult(boolean success, String errorMessage, String errorCode) {
        this.success = success;
        this.errorMessage = errorMessage;
        this.errorCode = errorCode;
    }
    
    /**
     * 创建成功结果
     * 
     * @return 成功的保存结果
     */
    public static TestDataSaveResult success() {
        return new TestDataSaveResult(true, null, null);
    }
    
    /**
     * 创建失败结果
     * 
     * @param errorMessage 错误消息
     * @return 失败的保存结果
     */
    public static TestDataSaveResult failure(String errorMessage) {
        return new TestDataSaveResult(false, errorMessage, null);
    }
    
    /**
     * 创建失败结果
     * 
     * @param errorMessage 错误消息
     * @param errorCode 错误代码
     * @return 失败的保存结果
     */
    public static TestDataSaveResult failure(String errorMessage, String errorCode) {
        return new TestDataSaveResult(false, errorMessage, errorCode);
    }
    
    /**
     * 创建失败结果（基于异常）
     * 
     * @param exception 异常对象
     * @return 失败的保存结果
     */
    public static TestDataSaveResult failure(Exception exception) {
        return new TestDataSaveResult(false, exception.getMessage(), exception.getClass().getSimpleName());
    }
    
    /**
     * 获取操作是否成功
     * 
     * @return true表示成功，false表示失败
     */
    public boolean isSuccess() {
        return success;
    }
    
    /**
     * 获取操作是否失败
     * 
     * @return true表示失败，false表示成功
     */
    public boolean isFailure() {
        return !success;
    }
    
    /**
     * 获取错误消息
     * 
     * @return 错误消息，成功时为null
     */
    public String getErrorMessage() {
        return errorMessage;
    }
    
    /**
     * 获取错误代码
     * 
     * @return 错误代码，成功时为null
     */
    public String getErrorCode() {
        return errorCode;
    }
    
    /**
     * 检查是否有错误消息
     * 
     * @return true表示有错误消息，false表示没有
     */
    public boolean hasErrorMessage() {
        return errorMessage != null && !errorMessage.trim().isEmpty();
    }
    
    /**
     * 检查是否有错误代码
     * 
     * @return true表示有错误代码，false表示没有
     */
    public boolean hasErrorCode() {
        return errorCode != null && !errorCode.trim().isEmpty();
    }
    
    @Override
    public String toString() {
        if (success) {
            return "TestDataSaveResult{success=true}";
        } else {
            return "TestDataSaveResult{" +
                    "success=false" +
                    ", errorMessage='" + errorMessage + '\'' +
                    ", errorCode='" + errorCode + '\'' +
                    '}';
        }
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        TestDataSaveResult that = (TestDataSaveResult) o;
        
        if (success != that.success) return false;
        if (errorMessage != null ? !errorMessage.equals(that.errorMessage) : that.errorMessage != null)
            return false;
        return errorCode != null ? errorCode.equals(that.errorCode) : that.errorCode == null;
    }
    
    @Override
    public int hashCode() {
        int result = (success ? 1 : 0);
        result = 31 * result + (errorMessage != null ? errorMessage.hashCode() : 0);
        result = 31 * result + (errorCode != null ? errorCode.hashCode() : 0);
        return result;
    }
}