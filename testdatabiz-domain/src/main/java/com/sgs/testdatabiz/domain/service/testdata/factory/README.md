# 测试数据工厂类

本包包含用于创建测试数据相关对象的工厂类，遵循工厂模式设计原则，将对象构造逻辑从主业务逻辑中分离出来。

## 工厂类概述

### TestDataObjectRelFactory
负责创建和管理 `TestDataObjectRelPO` 对象的工厂类。

**主要功能：**
- 从 `ReportTestDataInfo` 创建标准的对象关系
- 创建原始对象关系（用于处理 originalReportNo 场景）
- 设置对象为无效状态
- 更新业务版本ID

**使用示例：**
```java
@Autowired
private TestDataObjectRelFactory objectRelFactory;

// 创建标准对象关系
TestDataObjectRelPO objectRel = objectRelFactory.createObjectRelation(reportTestDataInfo);

// 创建原始对象关系
TestDataObjectRelPO originalObjectRel = objectRelFactory.createOriginalObjectRelation(reportTestDataInfo);

// 设置为无效状态
objectRelFactory.markAsInactive(existingObjectRel);
```

### TestDataMatrixFactory
负责创建和管理 `TestDataMatrixInfoPO` 对象的工厂类。

**主要功能：**
- 从 `TestDataTestMatrixInfo` 创建测试矩阵对象
- 处理扩展字段信息的构建
- 处理多语言信息的构建
- 更新业务版本ID
- 设置矩阵为无效状态

**使用示例：**
```java
@Autowired
private TestDataMatrixFactory matrixFactory;

// 创建测试矩阵
TestDataMatrixInfoPO matrix = matrixFactory.createTestMatrix(objectRel, testMatrixInfo);

// 更新业务版本ID
matrixFactory.updateBizVersionId(matrix);

// 设置为无效状态
matrixFactory.markAsInactive(matrix);
```

## 集成的值对象

工厂类集成了以下值对象来提供更好的封装和验证：

### BizVersionId
- 用于生成业务对象的MD5版本标识
- 自动处理字段标准化和MD5计算
- 支持 TestDataObjectRelPO、TestDataMatrixInfoPO 和 TestDataInfoPO

### TestDataSuffix
- 处理 LabCode 到数据库表后缀的转换
- 提供后缀验证功能

### SourceTypeIdentity
- 封装源类型到标识ID的映射
- 支持 StarLims 和 SLim 类型

### ConfigConstants
- 替换魔法字符串常量
- 提供配置值的枚举定义

## 设计原则

1. **单一职责原则**：每个工厂类只负责特定类型对象的创建
2. **依赖注入**：通过Spring的依赖注入管理工厂实例
3. **不可变性**：值对象设计为不可变，确保线程安全
4. **验证**：在对象创建时进行必要的参数验证
5. **封装**：隐藏复杂的对象构造逻辑，提供简洁的接口

## 错误处理

所有工厂方法都包含适当的参数验证：
- 对于null参数会抛出 `IllegalArgumentException`
- 对于无效的数据格式会进行适当的处理或忽略
- 提供清晰的错误消息帮助调试

## 测试

每个工厂类都有对应的单元测试：
- `TestDataObjectRelFactoryTest`
- `TestDataMatrixFactoryTest`

测试覆盖了正常场景、边界条件和异常情况。

## 使用建议

1. **依赖注入**：始终通过Spring的依赖注入使用工厂类
2. **异常处理**：适当处理工厂方法可能抛出的异常
3. **测试**：在使用工厂类的代码中编写相应的单元测试
4. **文档**：保持工厂方法的JavaDoc文档更新