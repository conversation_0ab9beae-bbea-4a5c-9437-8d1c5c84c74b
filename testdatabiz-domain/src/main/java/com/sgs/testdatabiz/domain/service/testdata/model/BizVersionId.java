package com.sgs.testdatabiz.domain.service.testdata.model;

import com.sgs.testdatabiz.core.util.NumberUtil;
import com.sgs.testdatabiz.core.util.StringUtil;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataInfoPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataMatrixInfoPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataObjectRelPO;

import lombok.Data;

import org.apache.commons.codec.digest.DigestUtils;

import java.util.Objects;

/**
 * 业务版本ID值对象
 * 封装MD5生成逻辑，用于生成业务对象的唯一标识
 * 
 * <AUTHOR>
 */
@Data
public class BizVersionId {
    
    private final String value;
    
    /**
     * 私有构造函数
     */
    private BizVersionId(String value) {
        this.value = value;
    }
    
    /**
     * 从TestDataObjectRelPO生成BizVersionId
     * 
     * @param objectRel 测试数据对象关系PO
     * @return BizVersionId实例
     */
    public static BizVersionId fromObjectRel(TestDataObjectRelPO objectRel) {
        if (objectRel == null) {
            throw new IllegalArgumentException("TestDataObjectRelPO cannot be null");
        }
        
        // 不需要创建副本，因为我们不修改原始对象
        // TestDataObjectRelPO copy = createObjectRelCopy(objectRel);
        
        // 标准化字段值，使用空字符串替换null值，避免String.format的%字符异常
        String productLineCode = emptyIfNull(objectRel.getProductLineCode());
        String labCode = emptyIfNull(objectRel.getLabCode());
        String orderNo = emptyIfNull(objectRel.getOrderNo());
        String parentOrderNo = emptyIfNull(objectRel.getParentOrderNo());
        String reportNo = emptyIfNull(objectRel.getReportNo());
        String objectNo = emptyIfNull(objectRel.getObjectNo());
        String externalId = emptyIfNull(objectRel.getExternalId());
        String externalNo = emptyIfNull(objectRel.getExternalNo());
        String externalObjectNo = emptyIfNull(objectRel.getExternalObjectNo());
        String languageId = String.valueOf(objectRel.getLanguageId() != null ? objectRel.getLanguageId() : 0);
        String sourceType = String.valueOf(objectRel.getSourceType() != null ? objectRel.getSourceType() : 0);
        String completeDate = String.valueOf(objectRel.getCompleteDate() != null ? objectRel.getCompleteDate() : "");
        String excludeCustomerInterface = emptyIfNull(objectRel.getExcludeCustomerInterface());
        
        String md5Input = String.format("%s%s%s%s%s%s%s%s%s%s%s%s%s",
                productLineCode, labCode, orderNo,
                parentOrderNo, reportNo, objectNo,
                externalId, externalNo, externalObjectNo,
                languageId, sourceType, completeDate,
                excludeCustomerInterface);
        
        return new BizVersionId(DigestUtils.md5Hex(md5Input));
    }
    
    /**
     * 从TestDataMatrixInfoPO生成BizVersionId
     * 
     * @param matrix 测试数据矩阵信息PO
     * @return BizVersionId实例
     */
    public static BizVersionId fromMatrix(TestDataMatrixInfoPO matrix) {
        if (matrix == null) {
            throw new IllegalArgumentException("TestDataMatrixInfoPO cannot be null");
        }
        
        // 不需要创建副本，因为我们不修改原始对象
        // TestDataMatrixInfoPO copy = createMatrixCopy(matrix);
        
        // 标准化字段值，使用空字符串替换null值，避免String.format的%字符异常
        String objectRelId = emptyIfNull(matrix.getObjectRelId());
        String testMatrixId = emptyIfNull(matrix.getTestMatrixId());
        String testLineMappingId = String.valueOf(NumberUtil.toInt(matrix.getTestLineMappingId()));
        String externalId = emptyIfNull(matrix.getExternalId());
        String externalCode = emptyIfNull(matrix.getExternalCode());
        String ppVersionId = String.valueOf(NumberUtil.toInt(matrix.getPpVersionId()));
        String aid = String.valueOf(NumberUtil.toLong(matrix.getAid()));
        String testLineId = String.valueOf(NumberUtil.toInt(matrix.getTestLineId()));
        String citationId = String.valueOf(NumberUtil.toInt(matrix.getCitationId()));
        String citationVersionId = String.valueOf(NumberUtil.toInt(matrix.getCitationVersionId()));
        String citationType = String.valueOf(NumberUtil.toInt(matrix.getCitationType()));
        String citationName = emptyIfNull(matrix.getCitationName());
        String sampleId = emptyIfNull(matrix.getSampleId());
        String testLineInstanceId = emptyIfNull(matrix.getTestLineInstanceId());
        String matrixSource = emptyIfNull(matrix.getMatrixSource());
        String sampleNo = emptyIfNull(matrix.getSampleNo());
        String externalSampleNo = emptyIfNull(matrix.getExternalSampleNo());
        String testLineSeq = String.valueOf(matrix.getTestLineSeq() != null ? matrix.getTestLineSeq() : 0);
        String sampleSeq = String.valueOf(matrix.getSampleSeq() != null ? matrix.getSampleSeq() : 0);
        String condition = emptyIfNull(matrix.getCondition());
        String evaluationAlias = emptyIfNull(matrix.getEvaluationAlias());
        String methodDesc = emptyIfNull(matrix.getMethodDesc());
        String conclusionId = emptyIfNull(matrix.getConclusionId());
        String conclusionDisplay = emptyIfNull(matrix.getConclusionDisplay());
        String extFields = emptyIfNull(matrix.getExtFields());
        
        String md5Input = String.format("%s%s%s%s%s%s%s%s%s%s%s%s%s%s%s%s%s%s%s%s%s%s%s%s%s%s",
                objectRelId, testMatrixId, testLineMappingId,
                externalId, externalCode, ppVersionId,
                aid, testLineId, citationId,
                citationVersionId, citationType, citationName,
                sampleId, testLineInstanceId, matrixSource,
                sampleNo, externalSampleNo, testLineSeq,
                sampleSeq, condition, evaluationAlias,
                methodDesc, conclusionId, conclusionDisplay,
                extFields);
        
        return new BizVersionId(DigestUtils.md5Hex(md5Input));
    }
    
    /**
     * 从TestDataInfoPO生成BizVersionId
     * 
     * @param testData 测试数据信息PO
     * @return BizVersionId实例
     */
    public static BizVersionId fromTestData(TestDataInfoPO testData) {
        if (testData == null) {
            throw new IllegalArgumentException("TestDataInfoPO cannot be null");
        }
        
        // 不需要创建副本，因为我们不修改原始对象
        // TestDataInfoPO copy = createTestDataCopy(testData);
        
        // 标准化字段值，使用空字符串替换null值，避免String.format的%字符异常
        String objectRelId = emptyIfNull(testData.getObjectRelId());
        String testMatrixId = emptyIfNull(testData.getTestMatrixId());
        String analyteCode = emptyIfNull(testData.getAnalyteCode());
        String analyteId = emptyIfNull(testData.getAnalyteId());
        String analyteName = emptyIfNull(testData.getAnalyteName());
        String analyteType = String.valueOf(NumberUtil.toInt(testData.getAnalyteType()));
        String analyteSeq = String.valueOf(NumberUtil.toInt(testData.getAnalyteSeq()));
        String reportUnit = emptyIfNull(testData.getReportUnit());
        String limitUnit = emptyIfNull(testData.getLimitUnit());
        String testValue = emptyIfNull(testData.getTestValue());
        String casNo = emptyIfNull(testData.getCasNo());
        String reportLimit = emptyIfNull(testData.getReportLimit());
        String conclusionId = emptyIfNull(testData.getConclusionId());
        String testResultType = emptyIfNull(testData.getTestResultType());
        String testDataId = emptyIfNull(testData.getTestDataId());
        String position = emptyIfNull(testData.getPosition());

        // 使用%%代替%字符，防止String.format解析异常
        String md5Input = String.format("%s%s%s%s%s%s%s%s%s%s%s%s%s%s%s%s",
                objectRelId, testMatrixId, analyteCode,
                analyteId, analyteName, analyteType,
                analyteSeq, reportUnit, limitUnit,
                testValue, casNo, reportLimit,
                conclusionId, testResultType, testDataId,
                position);
        
        return new BizVersionId(DigestUtils.md5Hex(md5Input));
    }
    
    /**
     * 直接从字符串创建BizVersionId
     * 
     * @param value MD5值
     * @return BizVersionId实例
     */
    public static BizVersionId of(String value) {
        if (value == null) {
            throw new IllegalArgumentException("BizVersionId value cannot be null");
        }
        return new BizVersionId(value);
    }
    
    /**
     * 获取MD5值
     * 
     * @return MD5字符串
     */
    public String getValue() {
        return value;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        BizVersionId that = (BizVersionId) o;
        return Objects.equals(value, that.value);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(value);
    }
    
    @Override
    public String toString() {
        return value;
    }
    
    // 添加辅助方法，将null值转换为空字符串
    private static String emptyIfNull(String str) {
        return str == null ? "" : str;
    }
    
    // 私有辅助方法用于创建对象副本（已不再使用）
    /*
    private static TestDataObjectRelPO createObjectRelCopy(TestDataObjectRelPO original) {
        TestDataObjectRelPO copy = new TestDataObjectRelPO();
        copy.setProductLineCode(original.getProductLineCode());
        copy.setLabCode(original.getLabCode());
        copy.setOrderNo(original.getOrderNo());
        copy.setParentOrderNo(original.getParentOrderNo());
        copy.setReportNo(original.getReportNo());
        copy.setObjectNo(original.getObjectNo());
        copy.setExternalId(original.getExternalId());
        copy.setExternalNo(original.getExternalNo());
        copy.setExternalObjectNo(original.getExternalObjectNo());
        copy.setLanguageId(original.getLanguageId());
        copy.setSourceType(original.getSourceType());
        copy.setCompleteDate(original.getCompleteDate());
        copy.setExcludeCustomerInterface(original.getExcludeCustomerInterface());
        return copy;
    }
    
    private static TestDataMatrixInfoPO createMatrixCopy(TestDataMatrixInfoPO original) {
        TestDataMatrixInfoPO copy = new TestDataMatrixInfoPO();
        copy.setObjectRelId(original.getObjectRelId());
        copy.setTestMatrixId(original.getTestMatrixId());
        copy.setTestLineMappingId(original.getTestLineMappingId());
        copy.setExternalId(original.getExternalId());
        copy.setExternalCode(original.getExternalCode());
        copy.setPpVersionId(original.getPpVersionId());
        copy.setAid(original.getAid());
        copy.setTestLineId(original.getTestLineId());
        copy.setCitationId(original.getCitationId());
        copy.setCitationVersionId(original.getCitationVersionId());
        copy.setCitationType(original.getCitationType());
        copy.setCitationName(original.getCitationName());
        copy.setSampleId(original.getSampleId());
        copy.setTestLineInstanceId(original.getTestLineInstanceId());
        copy.setMatrixSource(original.getMatrixSource());
        copy.setSampleNo(original.getSampleNo());
        copy.setExternalSampleNo(original.getExternalSampleNo());
        copy.setTestLineSeq(original.getTestLineSeq());
        copy.setSampleSeq(original.getSampleSeq());
        copy.setCondition(original.getCondition());
        copy.setEvaluationAlias(original.getEvaluationAlias());
        copy.setMethodDesc(original.getMethodDesc());
        copy.setConclusionId(original.getConclusionId());
        copy.setConclusionDisplay(original.getConclusionDisplay());
        copy.setExtFields(original.getExtFields());
        return copy;
    }
    
    private static TestDataInfoPO createTestDataCopy(TestDataInfoPO original) {
        TestDataInfoPO copy = new TestDataInfoPO();
        copy.setObjectRelId(original.getObjectRelId());
        copy.setTestMatrixId(original.getTestMatrixId());
        copy.setAnalyteCode(original.getAnalyteCode());
        copy.setAnalyteId(original.getAnalyteId());
        copy.setAnalyteName(original.getAnalyteName());
        copy.setAnalyteType(original.getAnalyteType());
        copy.setAnalyteSeq(original.getAnalyteSeq());
        copy.setReportUnit(original.getReportUnit());
        copy.setLimitUnit(original.getLimitUnit());
        copy.setTestValue(original.getTestValue());
        copy.setCasNo(original.getCasNo());
        copy.setReportLimit(original.getReportLimit());
        copy.setConclusionId(original.getConclusionId());
        copy.setTestResultType(original.getTestResultType());
        copy.setTestDataId(original.getTestDataId());
        copy.setPosition(original.getPosition());
        return copy;
    }
    */
}